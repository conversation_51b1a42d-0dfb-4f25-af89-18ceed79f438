import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:voice_assistant/pages/home_page.dart';
import 'package:voice_assistant/pages/voice_chat_page.dart';
import 'package:voice_assistant/pages/object_recognition_page.dart';
import 'package:voice_assistant/services/theme_service.dart';

class MainNavigationPage extends StatefulWidget {
  const MainNavigationPage({super.key});

  @override
  _MainNavigationPageState createState() => _MainNavigationPageState();
}

class _MainNavigationPageState extends State<MainNavigationPage> {
  int _selectedIndex = 0;

  static const List<Widget> _widgetOptions = <Widget>[
    HomePage(), // Ta page d'accueil existante
    VoiceChatPage(),
    ObjectRecognitionPage(),
  ];

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    final themeService = Provider.of<ThemeService>(context);
    final currentTheme = themeService.getThemeData(context);

    return Scaffold(
      body: Center(child: _widgetOptions.elementAt(_selectedIndex)),
      bottomNavigationBar: BottomNavigationBar(
        items: const <BottomNavigationBarItem>[
          BottomNavigationBarItem(
            icon: Icon(Icons.home_outlined),
            activeIcon: Icon(Icons.home),
            label: 'Accueil',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.mic_outlined),
            activeIcon: Icon(Icons.mic),
            label: 'Chat Vocal',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.camera_alt_outlined),
            activeIcon: Icon(Icons.camera_alt),
            label: 'Objets',
          ),
        ],
        currentIndex: _selectedIndex,
        selectedItemColor: currentTheme.colorScheme.secondary,
        unselectedItemColor:
            currentTheme.textTheme.bodySmall?.color ?? Colors.grey,
        backgroundColor:
            currentTheme.cardColor, // ou primaryColor selon le design souhaité
        type:
            BottomNavigationBarType
                .fixed, // Pour afficher les labels même si non sélectionnés
        onTap: _onItemTapped,
      ),
    );
  }
}
