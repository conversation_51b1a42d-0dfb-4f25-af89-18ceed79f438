import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/voice_chat_service.dart';
import '../services/gemini_chat_service.dart';

class VoiceChatPage extends StatefulWidget {
  const VoiceChatPage({super.key});

  @override
  State<VoiceChatPage> createState() => _VoiceChatPageState();
}

class _VoiceChatPageState extends State<VoiceChatPage>
    with TickerProviderStateMixin {
  late VoiceChatService _voiceChatService;
  late AnimationController _pulseController;
  late AnimationController _waveController;
  final TextEditingController _messageController = TextEditingController();
  bool _isInitializing = true;

  @override
  void initState() {
    super.initState();
    _voiceChatService = VoiceChatService();

    // Animations
    _pulseController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    )..repeat(reverse: true);

    _waveController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat();

    // Initialiser avec la clé API définie dans le service Gemini
    _initializeService();
  }

  Future<void> _initializeService() async {
    try {
      final success = await _voiceChatService.initialize();
      if (mounted) {
        setState(() {
          _isInitializing = false;
        });
        if (!success) {
          _showSnackBar('Erreur d\'initialisation du service', isError: true);
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isInitializing = false;
        });
        _showSnackBar('Erreur: $e', isError: true);
      }
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _waveController.dispose();
    _messageController.dispose();
    _voiceChatService.dispose();
    super.dispose();
  }

  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.green,
        duration: Duration(seconds: isError ? 4 : 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _voiceChatService,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Assistant Vocal'),
          backgroundColor: Colors.transparent,
          elevation: 0,
          centerTitle: true,
          titleTextStyle: const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
          actions: [
            Container(
              margin: const EdgeInsets.only(right: 16),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(12),
              ),
              child: IconButton(
                icon: const Icon(Icons.refresh_rounded, color: Colors.black54),
                onPressed: () {
                  _voiceChatService.clearHistory();
                  _showSnackBar('Conversation réinitialisée');
                },
                tooltip: 'Nouvelle conversation',
              ),
            ),
          ],
        ),
        body: _isInitializing ? _buildLoadingScreen() : _buildChatInterface(),
      ),
    );
  }

  Widget _buildLoadingScreen() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(20),
            ),
            child: const CircularProgressIndicator(
              strokeWidth: 3,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
            ),
          ),
          const SizedBox(height: 24),
          const Text(
            'Initialisation de l\'assistant...',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChatInterface() {
    return Consumer<VoiceChatService>(
      builder: (context, service, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.blue.shade50.withValues(alpha: 0.3),
                Colors.white,
              ],
            ),
          ),
          child: Column(
            children: [
              // Messages
              Expanded(child: _buildMessagesList(service)),

              // Statut
              _buildStatusBar(service),

              // Contrôles
              _buildControls(service),
            ],
          ),
        );
      },
    );
  }

  Widget _buildMessagesList(VoiceChatService service) {
    final messages = service.geminiService.messages;

    if (messages.isEmpty) {
      return Center(
        child: Container(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(24),
                ),
                child: Icon(
                  Icons.waving_hand_rounded,
                  size: 48,
                  color: Colors.blue.shade400,
                ),
              ),
              const SizedBox(height: 24),
              const Text(
                'Bonjour !',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Appuyez sur le microphone pour commencer\nune conversation',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey.shade600,
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      itemCount: messages.length,
      itemBuilder: (context, index) {
        final message = messages[index];
        return _buildMessageBubble(message);
      },
    );
  }

  Widget _buildMessageBubble(ChatMessage message) {
    return Align(
      alignment: message.isUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 8),
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.75,
        ),
        decoration: BoxDecoration(
          gradient:
              message.isUser
                  ? LinearGradient(
                    colors: [Colors.blue.shade400, Colors.blue.shade600],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  )
                  : null,
          color: message.isUser ? null : Colors.grey.shade100,
          borderRadius: BorderRadius.only(
            topLeft: const Radius.circular(24),
            topRight: const Radius.circular(24),
            bottomLeft:
                message.isUser
                    ? const Radius.circular(24)
                    : const Radius.circular(8),
            bottomRight:
                message.isUser
                    ? const Radius.circular(8)
                    : const Radius.circular(24),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.08),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              message.text,
              style: TextStyle(
                color: message.isUser ? Colors.white : Colors.black87,
                fontSize: 16,
                fontWeight: FontWeight.w500,
                height: 1.4,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              message.formattedTime,
              style: TextStyle(
                color:
                    message.isUser
                        ? Colors.white.withValues(alpha: 0.8)
                        : Colors.grey.shade500,
                fontSize: 12,
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusBar(VoiceChatService service) {
    String status = 'Prêt à vous écouter';
    Color statusColor = Colors.green;
    IconData statusIcon = Icons.check_circle_outline;

    if (service.isListening) {
      status = 'J\'écoute... "${service.lastRecognizedText}"';
      statusColor = Colors.blue;
      statusIcon = Icons.mic_rounded;
    } else if (service.isProcessing) {
      status = 'Traitement en cours...';
      statusColor = Colors.orange;
      statusIcon = Icons.psychology_rounded;
    } else if (service.isSpeaking) {
      status = 'Réponse en cours...';
      statusColor = Colors.purple;
      statusIcon = Icons.volume_up_rounded;
    } else if (service.lastError.isNotEmpty) {
      status = 'Erreur: ${service.lastError}';
      statusColor = Colors.red;
      statusIcon = Icons.error_outline_rounded;
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: statusColor.withValues(alpha: 0.3), width: 1),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(statusIcon, color: statusColor, size: 18),
          const SizedBox(width: 8),
          Flexible(
            child: Text(
              status,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: statusColor,
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildControls(VoiceChatService service) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(32),
          topRight: Radius.circular(32),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Column(
        children: [
          // Bouton microphone principal élégant
          GestureDetector(
            onTap:
                service.isBusy
                    ? null
                    : () {
                      service.startVoiceConversation();
                    },
            child: AnimatedBuilder(
              animation:
                  service.isListening ? _pulseController : _waveController,
              builder: (context, child) {
                return Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient:
                        service.isBusy
                            ? LinearGradient(
                              colors: [
                                Colors.orange.shade300,
                                Colors.orange.shade600,
                              ],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            )
                            : LinearGradient(
                              colors: [
                                Colors.blue.shade400,
                                Colors.blue.shade700,
                              ],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                    boxShadow: [
                      BoxShadow(
                        color: (service.isBusy ? Colors.orange : Colors.blue)
                            .withValues(alpha: 0.3),
                        blurRadius:
                            service.isListening
                                ? 25 * _pulseController.value
                                : 15,
                        spreadRadius:
                            service.isListening
                                ? 8 * _pulseController.value
                                : 0,
                      ),
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Icon(
                    service.isListening
                        ? Icons.mic_rounded
                        : Icons.mic_none_rounded,
                    color: Colors.white,
                    size: 40,
                  ),
                );
              },
            ),
          ),

          const SizedBox(height: 24),

          // Contrôles secondaires élégants
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildSecondaryButton(
                icon: Icons.stop_rounded,
                label: 'Arrêter',
                onPressed: service.isBusy ? service.stopAll : null,
                color: Colors.red,
              ),
              _buildSecondaryButton(
                icon: Icons.volume_off_rounded,
                label: 'Silence',
                onPressed: service.isSpeaking ? service.stopSpeaking : null,
                color: Colors.purple,
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Input texte élégant
          Container(
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(24),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _messageController,
                    decoration: InputDecoration(
                      hintText: 'Tapez votre message...',
                      hintStyle: TextStyle(color: Colors.grey.shade500),
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 16,
                      ),
                    ),
                    style: const TextStyle(fontSize: 16),
                    onSubmitted: (text) {
                      if (text.isNotEmpty && !service.isBusy) {
                        service.sendTextMessage(text);
                        _messageController.clear();
                      }
                    },
                  ),
                ),
                Container(
                  margin: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Colors.blue.shade400, Colors.blue.shade600],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: IconButton(
                    onPressed: () {
                      final text = _messageController.text.trim();
                      if (text.isNotEmpty && !service.isBusy) {
                        service.sendTextMessage(text);
                        _messageController.clear();
                      }
                    },
                    icon: const Icon(Icons.send_rounded, color: Colors.white),
                    tooltip: 'Envoyer',
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSecondaryButton({
    required IconData icon,
    required String label,
    required VoidCallback? onPressed,
    required Color color,
  }) {
    return Column(
      children: [
        Container(
          width: 56,
          height: 56,
          decoration: BoxDecoration(
            color:
                onPressed != null
                    ? color.withValues(alpha: 0.1)
                    : Colors.grey.shade100,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color:
                  onPressed != null
                      ? color.withValues(alpha: 0.3)
                      : Colors.grey.shade300,
            ),
          ),
          child: IconButton(
            onPressed: onPressed,
            icon: Icon(
              icon,
              color: onPressed != null ? color : Colors.grey.shade400,
              size: 24,
            ),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: onPressed != null ? color : Colors.grey.shade400,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }
}
