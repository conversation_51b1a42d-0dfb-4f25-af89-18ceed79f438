import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:camera/camera.dart';
import 'package:voice_assistant/services/theme_service.dart';
import 'package:voice_assistant/services/object_recognition_service.dart';
import 'package:voice_assistant/services/tts_service.dart';
import 'package:permission_handler/permission_handler.dart';

class ObjectRecognitionPage extends StatefulWidget {
  const ObjectRecognitionPage({super.key});

  @override
  State<ObjectRecognitionPage> createState() => _ObjectRecognitionPageState();
}

class _ObjectRecognitionPageState extends State<ObjectRecognitionPage>
    with TickerProviderStateMixin {
  late final ObjectRecognitionService _recognitionService;
  final TtsService _ttsService = TtsService();

  late AnimationController _pulseController;
  late AnimationController _fadeController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _fadeAnimation;

  bool _isInitializing = false;
  bool _hasPermission = false;
  String _statusMessage = 'Initialisation...';

  // Configuration pour utilisateurs malvoyants
  bool _audioFeedbackEnabled = true;
  bool _confidenceAnnouncementEnabled = true;
  bool _continuousAnnouncementEnabled = false;
  double _announcementInterval = 3.0;
  bool _isSpeaking = false;

  @override
  void initState() {
    super.initState();
    _recognitionService = ObjectRecognitionService();
    _setupAnimations();
    _initializeService();
  }

  void _setupAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _fadeController, curve: Curves.easeIn));

    _pulseController.repeat(reverse: true);
    _fadeController.forward();
  }

  Future<void> _initializeService() async {
    setState(() {
      _isInitializing = true;
      _statusMessage = 'Vérification des permissions...';
    });

    // Vérifier les permissions
    final cameraPermission = await Permission.camera.request();

    if (cameraPermission.isGranted) {
      setState(() {
        _hasPermission = true;
        _statusMessage = 'Initialisation de la caméra...';
      });

      // Initialiser le service
      final success = await _recognitionService.initialize();

      if (success) {
        setState(() {
          _statusMessage = 'Prêt pour la reconnaissance !';
        });

        // Démarrer l'écoute des changements
        _recognitionService.addListener(_onRecognitionUpdate);

        // Démarrer automatiquement la détection
        await Future.delayed(const Duration(milliseconds: 500));
        if (mounted) {
          await _recognitionService.startDetection();
          await _ttsService.speak(
            'Détection d\'objets activée. Je vais vous décrire ce que je vois.',
          );
        }
      } else {
        setState(() {
          _statusMessage = 'Erreur d\'initialisation';
        });
        await _ttsService.speak(
          'Erreur lors de l\'initialisation de la reconnaissance d\'objets',
        );
      }
    } else {
      setState(() {
        _hasPermission = false;
        _statusMessage = 'Permission caméra requise';
      });
      await _ttsService.speak(
        'Permission caméra requise pour la reconnaissance d\'objets',
      );
    }

    setState(() {
      _isInitializing = false;
    });
  }

  void _onRecognitionUpdate() {
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> _toggleDetection() async {
    if (_recognitionService.isDetecting) {
      await _recognitionService.stopDetection();
      await _ttsService.speak('Détection arrêtée');
    } else {
      await _recognitionService.startDetection();
      await _ttsService.speak('Détection activée');
    }
  }

  void _toggleAudioFeedback() {
    _recognitionService.toggleAudioFeedback();
    setState(() {
      _audioFeedbackEnabled = _recognitionService.audioFeedbackEnabled;
    });
  }

  void _toggleConfidenceAnnouncement() {
    _recognitionService.toggleConfidenceAnnouncement();
    setState(() {
      _confidenceAnnouncementEnabled =
          _recognitionService.confidenceAnnouncementEnabled;
    });
  }

  void _toggleContinuousAnnouncement() {
    _recognitionService.toggleContinuousAnnouncement();
    setState(() {
      _continuousAnnouncementEnabled =
          _recognitionService.continuousAnnouncementEnabled;
    });
  }

  void _takePhoto() async {
    if (!_recognitionService.isDetecting) {
      await _ttsService.speak('Détection non active');
      return;
    }

    await _ttsService.speak('Prise de photo en cours...');
    final detections = await _recognitionService.detectFromPhoto();

    if (detections.isEmpty) {
      await _ttsService.speak('Aucun objet détecté dans cette photo');
    }
  }

  void _announceCurrentObjects() async {
    final objects = _recognitionService.detectedObjects;
    if (objects.isEmpty) {
      await _ttsService.speak('Aucun objet détecté actuellement');
    } else {
      final message = _buildAnnouncementMessage(objects);
      await _ttsService.speak(message);
    }
  }

  String _buildAnnouncementMessage(List<DetectedObject> objects) {
    if (objects.isEmpty) return 'Aucun objet détecté';

    final objectGroups = <String, List<DetectedObject>>{};
    for (final obj in objects) {
      final classNameFr = _recognitionService.translateToFrench(obj.className);
      objectGroups.putIfAbsent(classNameFr, () => []).add(obj);
    }

    final messages = <String>[];
    for (final entry in objectGroups.entries) {
      final className = entry.key;
      final objectsOfClass = entry.value;

      if (objectsOfClass.length == 1) {
        final obj = objectsOfClass.first;
        final confidence = (obj.confidence * 100).round();
        messages.add('$className avec $confidence% de confiance');
      } else {
        final avgConfidence =
            (objectsOfClass
                        .map((obj) => obj.confidence)
                        .reduce((a, b) => a + b) /
                    objectsOfClass.length *
                    100)
                .round();
        messages.add(
          '${objectsOfClass.length} $className avec $avgConfidence% de confiance moyenne',
        );
      }
    }

    return 'Je vois: ${messages.join(', ')}';
  }

  @override
  Widget build(BuildContext context) {
    final themeService = Provider.of<ThemeService>(context);
    final isDark =
        themeService.getThemeData(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDark ? Colors.black : Colors.white,
      appBar: AppBar(
        title: const Text('Reconnaissance d\'Objets'),
        backgroundColor: isDark ? Colors.grey[900] : Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.volume_up),
            onPressed: _announceCurrentObjects,
            tooltip: 'Annoncer les objets détectés',
          ),
        ],
      ),
      body: _buildBody(),
      floatingActionButton: _buildFloatingActionButton(),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  Widget _buildBody() {
    if (_isInitializing) {
      return _buildLoadingView();
    }

    if (!_hasPermission) {
      return _buildPermissionView();
    }

    if (!_recognitionService.isInitialized) {
      return _buildErrorView();
    }

    return Column(
      children: [
        // Zone de la caméra avec détections
        Expanded(flex: 3, child: _buildCameraView()),

        // Contrôles pour utilisateurs malvoyants
        Expanded(flex: 2, child: _buildAccessibilityControls()),

        // Liste des objets détectés
        Expanded(flex: 2, child: _buildDetectedObjectsList()),
      ],
    );
  }

  Widget _buildLoadingView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _pulseAnimation.value,
                child: Icon(Icons.camera_alt, size: 80, color: Colors.blue),
              );
            },
          ),
          const SizedBox(height: 20),
          Text(
            _statusMessage,
            style: const TextStyle(fontSize: 18),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          const CircularProgressIndicator(),
        ],
      ),
    );
  }

  Widget _buildPermissionView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.camera_alt_outlined, size: 80, color: Colors.grey),
          const SizedBox(height: 20),
          const Text(
            'Permission caméra requise',
            style: TextStyle(fontSize: 18),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          ElevatedButton(
            onPressed: () async {
              final permission = await Permission.camera.request();
              if (permission.isGranted) {
                _initializeService();
              }
            },
            child: const Text('Demander la permission'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 80, color: Colors.red),
          const SizedBox(height: 20),
          const Text(
            'Erreur d\'initialisation',
            style: TextStyle(fontSize: 18),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          ElevatedButton(
            onPressed: _initializeService,
            child: const Text('Réessayer'),
          ),
        ],
      ),
    );
  }

  Widget _buildCameraView() {
    if (_recognitionService.cameraController == null) {
      return Container(
        color: Colors.black,
        child: const Center(
          child: Text(
            'Caméra non disponible',
            style: TextStyle(color: Colors.white),
          ),
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue, width: 2),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(10),
        child: Stack(
          children: [
            // Aperçu de la caméra
            CameraPreview(_recognitionService.cameraController!),

            // Overlay des détections
            if (_recognitionService.isDetecting)
              Positioned.fill(
                child: CustomPaint(
                  painter: DetectionPainter(
                    _recognitionService.detectedObjects,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccessibilityControls() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Contrôles Audio',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          // Bouton retour audio
          _buildControlButton(
            icon: _audioFeedbackEnabled ? Icons.volume_up : Icons.volume_off,
            label: 'Retour Audio',
            isActive: _audioFeedbackEnabled,
            onPressed: _toggleAudioFeedback,
          ),

          const SizedBox(height: 8),

          // Bouton annonce confiance
          _buildControlButton(
            icon:
                _confidenceAnnouncementEnabled
                    ? Icons.analytics
                    : Icons.analytics_outlined,
            label: 'Annonce Confiance',
            isActive: _confidenceAnnouncementEnabled,
            onPressed: _toggleConfidenceAnnouncement,
          ),

          const SizedBox(height: 8),

          // Bouton annonces continues
          _buildControlButton(
            icon:
                _continuousAnnouncementEnabled
                    ? Icons.repeat
                    : Icons.repeat_one,
            label: 'Annonces Continues',
            isActive: _continuousAnnouncementEnabled,
            onPressed: _toggleContinuousAnnouncement,
          ),
        ],
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required String label,
    required bool isActive,
    required VoidCallback onPressed,
  }) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: onPressed,
        icon: Icon(icon, color: isActive ? Colors.white : Colors.grey),
        label: Text(
          label,
          style: TextStyle(
            color: isActive ? Colors.white : Colors.grey,
            fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: isActive ? Colors.blue : Colors.grey[300],
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      ),
    );
  }

  Widget _buildDetectedObjectsList() {
    final objects = _recognitionService.detectedObjects;

    if (objects.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: const Center(
          child: Text(
            'Aucun objet détecté',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Objets Détectés (${objects.length})',
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Expanded(
            child: ListView.builder(
              itemCount: objects.length,
              itemBuilder: (context, index) {
                final obj = objects[index];
                final classNameFr = _recognitionService.translateToFrench(
                  obj.className,
                );
                final confidence = (obj.confidence * 100).round();

                return Card(
                  margin: const EdgeInsets.symmetric(vertical: 4),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: Colors.blue,
                      child: Text(
                        '${index + 1}',
                        style: const TextStyle(color: Colors.white),
                      ),
                    ),
                    title: Text(
                      classNameFr,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Text('Confiance: $confidence%'),
                    trailing: Text(
                      '${confidence}%',
                      style: TextStyle(
                        color:
                            confidence > 80
                                ? Colors.green
                                : confidence > 60
                                ? Colors.orange
                                : Colors.red,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Bouton photo
        FloatingActionButton(
          onPressed: _takePhoto,
          heroTag: 'photo',
          backgroundColor: Colors.orange,
          child: const Icon(Icons.camera_alt, color: Colors.white),
        ),

        // Bouton principal (démarrer/arrêter)
        FloatingActionButton.extended(
          onPressed: _toggleDetection,
          heroTag: 'detection',
          backgroundColor:
              _recognitionService.isDetecting ? Colors.red : Colors.green,
          icon: Icon(
            _recognitionService.isDetecting ? Icons.stop : Icons.play_arrow,
            color: Colors.white,
          ),
          label: Text(
            _recognitionService.isDetecting ? 'Arrêter' : 'Démarrer',
            style: const TextStyle(color: Colors.white),
          ),
        ),

        // Bouton annoncer
        FloatingActionButton(
          onPressed: _announceCurrentObjects,
          heroTag: 'announce',
          backgroundColor: Colors.blue,
          child: const Icon(Icons.volume_up, color: Colors.white),
        ),
      ],
    );
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _fadeController.dispose();
    _recognitionService.dispose();
    super.dispose();
  }
}

/// Painter pour dessiner les boîtes de détection
class DetectionPainter extends CustomPainter {
  final List<DetectedObject> detectedObjects;

  DetectionPainter(this.detectedObjects);

  @override
  void paint(Canvas canvas, Size size) {
    if (detectedObjects.isEmpty) return;

    final paint =
        Paint()
          ..style = PaintingStyle.stroke
          ..strokeWidth = 4.0;

    final textPainter = TextPainter(textDirection: TextDirection.ltr);

    for (int i = 0; i < detectedObjects.length; i++) {
      final obj = detectedObjects[i];

      // Couleurs vives pour une meilleure visibilité
      final colors = [
        Colors.red,
        Colors.blue,
        Colors.green,
        Colors.orange,
        Colors.purple,
        Colors.cyan,
        Colors.pink,
        Colors.yellow,
      ];
      final color = colors[i % colors.length];

      paint.color = color;

      // Calculer les coordonnées de la boîte (coordonnées normalisées 0-1)
      final left = obj.x * size.width;
      final top = obj.y * size.height;
      final right = (obj.x + obj.width) * size.width;
      final bottom = (obj.y + obj.height) * size.height;

      // Dessiner la boîte de détection avec des coins arrondis
      final rect = Rect.fromLTRB(left, top, right, bottom);
      final rrect = RRect.fromRectAndRadius(rect, const Radius.circular(8));
      canvas.drawRRect(rrect, paint);

      // Dessiner des coins pour marquer la détection
      final cornerPaint =
          Paint()
            ..color = color
            ..style = PaintingStyle.fill
            ..strokeWidth = 6.0;

      // Coins supérieurs
      canvas.drawLine(Offset(left, top), Offset(left + 20, top), cornerPaint);
      canvas.drawLine(Offset(left, top), Offset(left, top + 20), cornerPaint);
      canvas.drawLine(Offset(right, top), Offset(right - 20, top), cornerPaint);
      canvas.drawLine(Offset(right, top), Offset(right, top + 20), cornerPaint);

      // Coins inférieurs
      canvas.drawLine(
        Offset(left, bottom),
        Offset(left + 20, bottom),
        cornerPaint,
      );
      canvas.drawLine(
        Offset(left, bottom),
        Offset(left, bottom - 20),
        cornerPaint,
      );
      canvas.drawLine(
        Offset(right, bottom),
        Offset(right - 20, bottom),
        cornerPaint,
      );
      canvas.drawLine(
        Offset(right, bottom),
        Offset(right, bottom - 20),
        cornerPaint,
      );

      // Préparer le texte du label
      final confidence = (obj.confidence * 100).round();
      final labelText = '${obj.className} $confidence%';
      textPainter.text = TextSpan(
        text: labelText,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 16,
          fontWeight: FontWeight.bold,
          shadows: [
            Shadow(offset: Offset(1, 1), blurRadius: 2, color: Colors.black),
          ],
        ),
      );
      textPainter.layout();

      // Position du label (au-dessus de la boîte)
      final labelTop = top - textPainter.height - 12;
      final labelLeft = left;

      // Dessiner le fond du label avec bordures arrondies
      final labelRect = RRect.fromRectAndRadius(
        Rect.fromLTWH(
          labelLeft - 8,
          labelTop - 4,
          textPainter.width + 16,
          textPainter.height + 8,
        ),
        const Radius.circular(8),
      );

      final labelPaint = Paint()..color = color.withValues(alpha: 0.9);
      canvas.drawRRect(labelRect, labelPaint);

      // Dessiner le texte du label
      textPainter.paint(canvas, Offset(labelLeft, labelTop));
    }
  }

  @override
  bool shouldRepaint(DetectionPainter oldDelegate) {
    return detectedObjects != oldDelegate.detectedObjects;
  }
}
