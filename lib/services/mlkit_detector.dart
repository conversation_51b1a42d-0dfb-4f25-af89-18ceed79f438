import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:camera/camera.dart';
import 'package:google_mlkit_object_detection/google_mlkit_object_detection.dart';
import 'object_recognition_service.dart' as custom;

/// Détecteur Google ML Kit utilisant vos labels personnalisés
/// Implémentation rapide et fiable pour la détection d'objets en temps réel
class MLKitDetector {
  static const String _labelsPath = 'assets/models/ssd_mobilenet.txt';

  // Détecteur Google ML Kit
  ObjectDetector? _objectDetector;

  // Labels des classes d'objets (vos vrais labels)
  final List<String> _labels = [];

  // Configuration
  bool _isInitialized = false;
  bool get isInitialized => _isInitialized;

  // Paramètres de détection
  double _confidenceThreshold = 0.5;
  static const int _detectionIntervalMs =
      300; // ~3 FPS pour de bonnes performances

  // Cache pour optimiser les détections
  DateTime? _lastDetectionTime;
  final List<custom.DetectedObject> _lastDetections = [];

  /// Initialise le détecteur Google ML Kit
  Future<bool> initialize() async {
    try {
      debugPrint('Initialisation du détecteur Google ML Kit...');

      // Charger vos labels personnalisés
      await _loadLabels();

      // Configurer le détecteur ML Kit
      final options = ObjectDetectorOptions(
        mode: DetectionMode.stream, // Mode streaming pour temps réel
        classifyObjects: true, // Classification des objets
        multipleObjects: true, // Détection multiple
      );

      _objectDetector = ObjectDetector(options: options);

      _isInitialized = true;
      debugPrint('Détecteur Google ML Kit initialisé avec succès');
      debugPrint('Chargé avec ${_labels.length} classes personnalisées');
      return true;
    } catch (e) {
      debugPrint('Erreur lors de l\'initialisation du détecteur: $e');
      _isInitialized = false;
      return false;
    }
  }

  /// Charge vos labels personnalisés depuis les assets
  Future<void> _loadLabels() async {
    try {
      debugPrint('Chargement des labels personnalisés...');

      final labelsData = await rootBundle.loadString(_labelsPath);
      final lines = labelsData.split('\n');

      _labels.clear();
      for (final line in lines) {
        final trimmed = line.trim();
        if (trimmed.isNotEmpty) {
          _labels.add(trimmed);
        }
      }

      debugPrint('Labels chargés: ${_labels.length} classes');
      debugPrint('Exemples: ${_labels.take(5).join(', ')}...');
    } catch (e) {
      debugPrint('Erreur lors du chargement des labels: $e');
      // Labels par défaut si le fichier n'existe pas
      _labels.addAll([
        'person',
        'bicycle',
        'car',
        'motorcycle',
        'airplane',
        'bus',
        'train',
        'truck',
        'boat',
        'traffic light',
        'fire hydrant',
        'stop sign',
        'parking meter',
        'bench',
        'bird',
        'cat',
        'dog',
        'horse',
        'sheep',
        'cow',
        'elephant',
        'bear',
        'zebra',
        'giraffe',
        'backpack',
        'umbrella',
        'handbag',
        'tie',
        'suitcase',
        'frisbee',
        'skis',
        'snowboard',
        'sports ball',
        'kite',
        'baseball bat',
        'baseball glove',
        'skateboard',
        'surfboard',
        'tennis racket',
        'bottle',
        'wine glass',
        'cup',
        'fork',
        'knife',
        'spoon',
        'bowl',
        'banana',
        'apple',
        'sandwich',
        'orange',
        'broccoli',
        'carrot',
        'hot dog',
        'pizza',
        'donut',
        'cake',
        'chair',
        'couch',
        'potted plant',
        'bed',
        'dining table',
        'toilet',
        'tv',
        'laptop',
        'mouse',
        'remote',
        'keyboard',
        'cell phone',
        'microwave',
        'oven',
        'toaster',
        'sink',
        'refrigerator',
        'book',
        'clock',
        'vase',
        'scissors',
        'teddy bear',
        'hair drier',
        'toothbrush',
      ]);
    }
  }

  /// Détecte les objets dans une image de caméra
  Future<List<custom.DetectedObject>> detectFromCameraImage(
    CameraImage cameraImage,
  ) async {
    if (!_isInitialized || _objectDetector == null) return [];

    try {
      // Optimisation : limiter la fréquence de détection
      final now = DateTime.now();
      if (_lastDetectionTime != null &&
          now.difference(_lastDetectionTime!).inMilliseconds <
              _detectionIntervalMs) {
        return _lastDetections;
      }
      _lastDetectionTime = now;

      // Convertir CameraImage en InputImage pour ML Kit
      final inputImage = _convertCameraImage(cameraImage);
      if (inputImage == null) return [];

      // Exécuter la détection ML Kit
      final detectedObjects = await _objectDetector!.processImage(inputImage);

      // Convertir les résultats ML Kit vers votre format
      final detections = _convertMLKitResults(detectedObjects);

      // Filtrer par seuil de confiance
      final filteredDetections =
          detections
              .where((obj) => obj.confidence >= _confidenceThreshold)
              .toList();

      _lastDetections.clear();
      _lastDetections.addAll(filteredDetections);

      return filteredDetections;
    } catch (e) {
      debugPrint('Erreur lors de la détection sur image caméra: $e');
      return [];
    }
  }

  /// Convertit CameraImage en InputImage pour ML Kit
  InputImage? _convertCameraImage(CameraImage cameraImage) {
    try {
      // Conversion simplifiée pour Google ML Kit
      final bytes = cameraImage.planes[0].bytes;
      final size = Size(
        cameraImage.width.toDouble(),
        cameraImage.height.toDouble(),
      );

      return InputImage.fromBytes(
        bytes: bytes,
        metadata: InputImageMetadata(
          size: size,
          rotation: InputImageRotation.rotation0deg,
          format: InputImageFormat.nv21,
          bytesPerRow: cameraImage.planes[0].bytesPerRow,
        ),
      );
    } catch (e) {
      debugPrint('Erreur lors de la conversion de l\'image: $e');
      return null;
    }
  }

  /// Convertit les résultats ML Kit vers votre format personnalisé
  List<custom.DetectedObject> _convertMLKitResults(
    List<DetectedObject> mlkitObjects,
  ) {
    final detections = <custom.DetectedObject>[];

    for (final obj in mlkitObjects) {
      // Utiliser le label ML Kit ou mapper vers vos labels personnalisés
      String className = 'unknown';
      double confidence = 0.0;

      if (obj.labels.isNotEmpty) {
        final bestLabel = obj.labels.first;
        className = _mapToCustomLabel(bestLabel.text);
        confidence = bestLabel.confidence;
      }

      // Normaliser les coordonnées de la bounding box
      final boundingBox = obj.boundingBox;

      detections.add(
        custom.DetectedObject(
          className: className,
          confidence: confidence,
          x: boundingBox.left / 1000.0, // Normaliser selon votre format
          y: boundingBox.top / 1000.0,
          width: boundingBox.width / 1000.0,
          height: boundingBox.height / 1000.0,
        ),
      );
    }

    return detections;
  }

  /// Mappe les labels ML Kit vers vos labels personnalisés
  String _mapToCustomLabel(String mlkitLabel) {
    // Recherche directe dans vos labels
    final lowerLabel = mlkitLabel.toLowerCase();

    for (final customLabel in _labels) {
      if (customLabel.toLowerCase() == lowerLabel) {
        return customLabel;
      }
    }

    // Recherche partielle
    for (final customLabel in _labels) {
      if (customLabel.toLowerCase().contains(lowerLabel) ||
          lowerLabel.contains(customLabel.toLowerCase())) {
        return customLabel;
      }
    }

    // Retourner le label ML Kit si pas de correspondance
    return mlkitLabel;
  }

  /// Configure le seuil de confiance
  void setConfidenceThreshold(double threshold) {
    _confidenceThreshold = threshold.clamp(0.0, 1.0);
  }

  /// Applique Non-Maximum Suppression pour éliminer les détections redondantes
  List<custom.DetectedObject> _applyNMS(
    List<custom.DetectedObject> detections,
  ) {
    if (detections.length <= 1) return detections;

    // Trier par confiance décroissante
    detections.sort((a, b) => b.confidence.compareTo(a.confidence));

    final filtered = <custom.DetectedObject>[];
    final suppressed = <bool>[];

    for (int i = 0; i < detections.length; i++) {
      suppressed.add(false);
    }

    for (int i = 0; i < detections.length; i++) {
      if (suppressed[i]) continue;

      filtered.add(detections[i]);

      for (int j = i + 1; j < detections.length; j++) {
        if (suppressed[j]) continue;

        final iou = _calculateIoU(detections[i], detections[j]);
        if (iou > 0.5) {
          // Seuil NMS
          suppressed[j] = true;
        }
      }
    }

    return filtered;
  }

  /// Calcule l'Intersection over Union entre deux détections
  double _calculateIoU(custom.DetectedObject a, custom.DetectedObject b) {
    final x1 = math.max(a.x, b.x);
    final y1 = math.max(a.y, b.y);
    final x2 = math.min(a.x + a.width, b.x + b.width);
    final y2 = math.min(a.y + a.height, b.y + b.height);

    if (x2 <= x1 || y2 <= y1) return 0.0;

    final intersection = (x2 - x1) * (y2 - y1);
    final areaA = a.width * a.height;
    final areaB = b.width * b.height;
    final union = areaA + areaB - intersection;

    return union > 0 ? intersection / union : 0.0;
  }

  /// Libère les ressources
  void dispose() {
    _objectDetector?.close();
    _objectDetector = null;
    _isInitialized = false;
  }
}
