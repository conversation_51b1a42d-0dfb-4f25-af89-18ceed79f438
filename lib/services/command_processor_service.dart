import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'reminder_service.dart';
import 'reminder_parser_service.dart';
import 'weather_service.dart';
import 'music_service.dart';
import 'web_search_service.dart';

/// Service orchestrateur principal pour traiter les commandes vocales
class CommandProcessorService extends ChangeNotifier {
  // Services existants
  final ReminderService _reminderService = ReminderService();
  final ReminderParserService _reminderParser = ReminderParserService();
  final WeatherService _weatherService = WeatherService();
  final MusicService _musicService = MusicService();
  final WebSearchService _webSearchService = WebSearchService();

  bool _isInitialized = false;
  String _lastError = '';

  // Getters
  bool get isInitialized => _isInitialized;
  String get lastError => _lastError;

  /// Initialise tous les services
  Future<bool> initialize() async {
    try {
      debugPrint('Initialisation du processeur de commandes...');

      // Les services existants n'ont pas de méthode initialize
      // Ils sont prêts à l'utilisation directement

      _isInitialized = true;
      _lastError = '';

      debugPrint('Processeur de commandes initialisé avec succès');
      notifyListeners();
      return true;
    } catch (e) {
      _lastError = 'Erreur d\'initialisation: $e';
      debugPrint(_lastError);
      notifyListeners();
      return false;
    }
  }

  /// Traite une commande utilisateur et retourne une réponse
  Future<String?> processCommand(String userInput) async {
    if (!_isInitialized) {
      return 'Service non initialisé';
    }

    final input = userInput.toLowerCase().trim();
    debugPrint('Traitement de la commande: $input');

    try {
      // 1. COMMANDES DE RAPPEL
      if (_isReminderCommand(input)) {
        return await _handleReminderCommand(input);
      }

      // 2. COMMANDES MÉTÉO
      if (_isWeatherCommand(input)) {
        return await _handleWeatherCommand(input);
      }

      // 3. COMMANDES MUSIQUE
      if (_isMusicCommand(input)) {
        return await _handleMusicCommand(input);
      }

      // 4. COMMANDES DE RECHERCHE
      if (_isSearchCommand(input)) {
        return await _handleSearchCommand(input);
      }

      // 5. COMMANDES D'APPLICATION
      if (_isAppCommand(input)) {
        return await _handleAppCommand(input);
      }

      // 6. COMMANDES SYSTÈME
      if (_isSystemCommand(input)) {
        return await _handleSystemCommand(input);
      }

      // Aucune commande spéciale trouvée
      return null;
    } catch (e) {
      _lastError = 'Erreur de traitement: $e';
      debugPrint(_lastError);
      return 'Désolé, une erreur s\'est produite lors du traitement de votre demande.';
    }
  }

  // ========== DÉTECTION DES COMMANDES ==========

  bool _isReminderCommand(String input) {
    return input.contains('rappelle') ||
        input.contains('rappel') ||
        input.contains('n\'oublie pas');
  }

  bool _isWeatherCommand(String input) {
    return input.contains('météo') ||
        input.contains('temps') ||
        input.contains('température') ||
        input.contains('pluie') ||
        input.contains('soleil');
  }

  bool _isMusicCommand(String input) {
    return input.contains('musique') ||
        input.contains('chanson') ||
        input.contains('joue') ||
        input.contains('écoute') ||
        input.contains('spotify');
  }

  bool _isSearchCommand(String input) {
    return input.contains('recherche') ||
        input.contains('cherche') ||
        input.contains('trouve') ||
        input.contains('google');
  }

  bool _isAppCommand(String input) {
    return input.contains('ouvre') ||
        input.contains('lance') ||
        input.contains('démarre');
  }

  bool _isSystemCommand(String input) {
    return input.contains('photo') ||
        input.contains('sms') ||
        input.contains('message') ||
        input.contains('appel');
  }

  // ========== GESTIONNAIRES DE COMMANDES ==========

  Future<String> _handleReminderCommand(String input) async {
    try {
      // Parser la commande de rappel
      final reminderData = _reminderParser.parseReminderCommand(input);
      if (reminderData != null) {
        final DateTime targetDate = reminderData['targetDate'];
        final String message = reminderData['message'];

        // Créer le rappel avec le service Firebase
        final reminderId = await _reminderService.addReminder(
          message,
          targetDate,
        );

        // Formater la confirmation
        final confirmation = _reminderParser.formatReminderConfirmation(
          targetDate,
          message,
        );
        return confirmation;
      } else {
        return 'Désolé, je n\'ai pas pu comprendre votre demande de rappel. Pouvez-vous reformuler ?';
      }
    } catch (e) {
      return 'Erreur lors de la création du rappel.';
    }
  }

  Future<String> _handleWeatherCommand(String input) async {
    try {
      // Utiliser le service météo existant - méthode pour position actuelle
      final weatherData = await _weatherService.getWeatherForCurrentLocation();
      if (weatherData != null) {
        // Utiliser la méthode de description existante
        return _weatherService.getWeatherDescription(weatherData);
      } else {
        return 'Désolé, je n\'ai pas pu récupérer les informations météo.';
      }
    } catch (e) {
      return 'Erreur lors de la récupération de la météo.';
    }
  }

  Future<String> _handleMusicCommand(String input) async {
    try {
      if (input.contains('joue') || input.contains('lance')) {
        // Utiliser la méthode playMusic du service existant
        final success = await _musicService.playMusic();
        if (success) {
          return 'Musique lancée !';
        } else {
          return 'Impossible de lancer la musique. Vérifiez qu\'une application musicale est installée.';
        }
      } else if (input.contains('spotify')) {
        final success = await _musicService.launchSpotify();
        if (success) {
          return 'Spotify lancé !';
        } else {
          return 'Impossible de lancer Spotify.';
        }
      } else if (input.contains('youtube music')) {
        final success = await _musicService.launchYouTubeMusic();
        if (success) {
          return 'YouTube Music lancé !';
        } else {
          return 'Impossible de lancer YouTube Music.';
        }
      }
      return 'Commande musicale non reconnue. Essayez "joue de la musique" ou "lance Spotify".';
    } catch (e) {
      return 'Erreur lors du contrôle de la musique.';
    }
  }

  Future<String> _handleSearchCommand(String input) async {
    try {
      // Extraire le terme de recherche
      String searchTerm = _extractSearchTerm(input);
      if (searchTerm.isNotEmpty) {
        final results = await _webSearchService.search(searchTerm);
        if (results.isNotEmpty) {
          return 'J\'ai trouvé ${results.length} résultats pour "$searchTerm". '
              'Le premier résultat est: ${results.first.title}';
        }
      }
      return 'Aucun résultat trouvé pour votre recherche.';
    } catch (e) {
      return 'Erreur lors de la recherche.';
    }
  }

  Future<String> _handleAppCommand(String input) async {
    // Extraire le nom de l'application
    String appName = _extractAppName(input);
    if (appName.isNotEmpty) {
      // TODO: Implémenter l'ouverture d'applications
      return 'Ouverture de $appName... (fonctionnalité à implémenter)';
    }
    return 'Application non reconnue.';
  }

  Future<String> _handleSystemCommand(String input) async {
    if (input.contains('photo')) {
      // TODO: Implémenter la prise de photo
      return 'Prise de photo... (fonctionnalité à implémenter)';
    } else if (input.contains('sms') || input.contains('message')) {
      // TODO: Implémenter l'envoi de SMS
      return 'Envoi de message... (fonctionnalité à implémenter)';
    }
    return 'Commande système non reconnue.';
  }

  // ========== UTILITAIRES ==========

  String _extractSearchTerm(String input) {
    // Regex pour extraire le terme après "recherche", "cherche", etc.
    final patterns = [
      RegExp(r'recherche (.+)', caseSensitive: false),
      RegExp(r'cherche (.+)', caseSensitive: false),
      RegExp(r'trouve (.+)', caseSensitive: false),
    ];

    for (final pattern in patterns) {
      final match = pattern.firstMatch(input);
      if (match != null) {
        return match.group(1)!.trim();
      }
    }
    return '';
  }

  String _extractAppName(String input) {
    // Regex pour extraire le nom après "ouvre", "lance", etc.
    final patterns = [
      RegExp(r'ouvre (.+)', caseSensitive: false),
      RegExp(r'lance (.+)', caseSensitive: false),
      RegExp(r'démarre (.+)', caseSensitive: false),
    ];

    for (final pattern in patterns) {
      final match = pattern.firstMatch(input);
      if (match != null) {
        return match.group(1)!.trim();
      }
    }
    return '';
  }

  @override
  void dispose() {
    // Les services existants n'ont pas de méthode dispose
    super.dispose();
  }
}
