import 'package:flutter/material.dart';
import 'package:google_generative_ai/google_generative_ai.dart';
import 'package:intl/intl.dart';

/// Service de chat avec Gemini AI (100% gratuit)
class GeminiChatService extends ChangeNotifier {
  static const String _apiKey =
      'AIzaSyCtPWqGz0wEQgO1lDzlMm_QMAZi0AtDLVU'; // Votre clé API Gemini

  GenerativeModel? _model;
  ChatSession? _chatSession;

  bool _isInitialized = false;
  bool _isLoading = false;
  String _lastError = '';

  // Getters
  bool get isInitialized => _isInitialized;
  bool get isLoading => _isLoading;
  String get lastError => _lastError;

  // Historique des messages
  final List<ChatMessage> _messages = [];
  List<ChatMessage> get messages => List.unmodifiable(_messages);

  /// Initialise le service Gemini
  Future<bool> initialize() async {
    try {
      debugPrint('Initialisation de Gemini AI...');

      // Clé API configurée dans le code, on continue

      // Créer le modèle Gemini
      _model = GenerativeModel(
        model: 'gemini-1.5-flash', // Modèle gratuit et rapide
        apiKey: _apiKey,
        generationConfig: GenerationConfig(
          temperature: 0.7, // Créativité modérée
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 1024, // Limite pour éviter les réponses trop longues
        ),
        safetySettings: [
          SafetySetting(HarmCategory.harassment, HarmBlockThreshold.medium),
          SafetySetting(HarmCategory.hateSpeech, HarmBlockThreshold.medium),
          SafetySetting(
            HarmCategory.sexuallyExplicit,
            HarmBlockThreshold.medium,
          ),
          SafetySetting(
            HarmCategory.dangerousContent,
            HarmBlockThreshold.medium,
          ),
        ],
      );

      // Démarrer une session de chat
      _chatSession = _model!.startChat(history: []);

      _isInitialized = true;
      _lastError = '';

      debugPrint('Gemini AI initialisé avec succès');
      notifyListeners();
      return true;
    } catch (e) {
      _lastError = 'Erreur d\'initialisation: $e';
      debugPrint(_lastError);
      notifyListeners();
      return false;
    }
  }

  /// Envoie un message à Gemini et retourne la réponse
  Future<String?> sendMessage(String message) async {
    if (!_isInitialized || _chatSession == null) {
      _lastError = 'Service non initialisé';
      return null;
    }

    try {
      _isLoading = true;
      _lastError = '';
      notifyListeners();

      // Ajouter le message utilisateur à l'historique
      _messages.add(
        ChatMessage(text: message, isUser: true, timestamp: DateTime.now()),
      );
      notifyListeners();

      // Vérifier les commandes directes avant d'envoyer à Gemini
      String? directResponse = _handleDirectCommands(message);
      if (directResponse != null) {
        _messages.add(
          ChatMessage(
            text: directResponse,
            isUser: false,
            timestamp: DateTime.now(),
          ),
        );
        _isLoading = false;
        notifyListeners();
        return directResponse;
      }

      debugPrint('Envoi du message à Gemini: $message');

      // Envoyer le message à Gemini
      final response = await _chatSession!.sendMessage(Content.text(message));
      final responseText = response.text;

      if (responseText != null && responseText.isNotEmpty) {
        // Ajouter la réponse de Gemini à l'historique
        _messages.add(
          ChatMessage(
            text: responseText,
            isUser: false,
            timestamp: DateTime.now(),
          ),
        );

        debugPrint('Réponse de Gemini: $responseText');

        _isLoading = false;
        notifyListeners();
        return responseText;
      } else {
        _lastError = 'Réponse vide de Gemini';
        _isLoading = false;
        notifyListeners();
        return null;
      }
    } catch (e) {
      _lastError = 'Erreur lors de l\'envoi: $e';
      debugPrint(_lastError);

      _isLoading = false;
      notifyListeners();
      return null;
    }
  }

  /// Gère les commandes directes sans passer par Gemini
  String? _handleDirectCommands(String message) {
    final input = message.toLowerCase().trim();

    // Commande heure
    if (input.contains('heure') || input.contains('temps il est')) {
      final now = DateTime.now();
      final formatter = DateFormat('HH:mm');
      return "Il est ${formatter.format(now)}.";
    }

    // Commande calcul simple
    if (input.contains('calcule') || input.contains('combien fait')) {
      final result = _handleMathQuery(input);
      if (result != null) {
        return "Le résultat est $result.";
      }
    }

    // Commande date
    if (input.contains('date') || input.contains('quel jour')) {
      final now = DateTime.now();
      final formatter = DateFormat('EEEE d MMMM yyyy', 'fr_FR');
      return "Nous sommes le ${formatter.format(now)}.";
    }

    return null; // Aucune commande directe trouvée
  }

  /// Gère les calculs mathématiques simples
  double? _handleMathQuery(String input) {
    // Regex pour détecter : "calcule 15 + 27" ou "combien fait 10 * 5"
    final regex = RegExp(
      r'(?:calcule|combien fait)\s*(\d+(?:\.\d+)?)\s*([+\-*/])\s*(\d+(?:\.\d+)?)',
    );
    final match = regex.firstMatch(input);

    if (match != null) {
      try {
        double a = double.parse(match.group(1)!);
        String op = match.group(2)!;
        double b = double.parse(match.group(3)!);

        switch (op) {
          case '+':
            return a + b;
          case '-':
            return a - b;
          case '*':
            return a * b;
          case '/':
            return b != 0 ? a / b : null;
        }
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  /// Efface l'historique des messages
  void clearHistory() {
    _messages.clear();

    // Redémarrer la session de chat
    if (_model != null) {
      _chatSession = _model!.startChat(history: []);
    }

    notifyListeners();
  }

  /// Configure la clé API (à appeler avant initialize)
  void setApiKey(String apiKey) {
    if (apiKey.isNotEmpty && apiKey != _apiKey) {
      // Réinitialiser si la clé change
      _isInitialized = false;
      _model = null;
      _chatSession = null;
    }
  }

  /// Obtient des statistiques d'utilisation
  Map<String, dynamic> getUsageStats() {
    final userMessages = _messages.where((m) => m.isUser).length;
    final aiMessages = _messages.where((m) => !m.isUser).length;

    return {
      'totalMessages': _messages.length,
      'userMessages': userMessages,
      'aiMessages': aiMessages,
      'isInitialized': _isInitialized,
      'lastError': _lastError,
    };
  }

  @override
  void dispose() {
    _chatSession = null;
    _model = null;
    _isInitialized = false;
    super.dispose();
  }
}

/// Classe pour représenter un message de chat
class ChatMessage {
  final String text;
  final bool isUser;
  final DateTime timestamp;

  ChatMessage({
    required this.text,
    required this.isUser,
    required this.timestamp,
  });

  /// Formate l'heure du message
  String get formattedTime {
    return '${timestamp.hour.toString().padLeft(2, '0')}:'
        '${timestamp.minute.toString().padLeft(2, '0')}';
  }
}

/// Service de configuration pour la clé API
class GeminiConfig {
  static const String _keyPreference = 'gemini_api_key';

  /// Sauvegarde la clé API de manière sécurisée
  static Future<void> saveApiKey(String apiKey) async {
    // Pour la démo, on utilise une variable statique
    // En production, utilisez flutter_secure_storage
    _savedApiKey = apiKey;
  }

  /// Récupère la clé API sauvegardée
  static Future<String?> getApiKey() async {
    return _savedApiKey;
  }

  static String? _savedApiKey;
}
