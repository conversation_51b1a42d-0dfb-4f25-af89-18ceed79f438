import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'mlkit_detector.dart';
import 'tts_service.dart';

/// Service de reconnaissance d'objets avec retour audio pour utilisateurs malvoyants
class ObjectRecognitionService extends ChangeNotifier {
  // Contrôleur de caméra
  CameraController? _cameraController;
  CameraController? get cameraController => _cameraController;

  // Détecteur Google ML Kit (remplace TensorFlow Lite)
  final MLKitDetector _mlkitDetector = MLKitDetector();

  // Service TTS pour les annonces audio
  final TtsService _ttsService = TtsService();

  // État du service
  bool _isInitialized = false;
  bool _isDetecting = false;
  bool _isModelLoaded = false;

  // Résultats de détection
  List<DetectedObject> _detectedObjects = [];
  List<DetectedObject> get detectedObjects => _detectedObjects;

  // Configuration audio pour malvoyants
  bool _audioFeedbackEnabled = true;
  bool _confidenceAnnouncementEnabled = true;
  bool _continuousAnnouncementEnabled = false;
  double _announcementInterval = 3.0; // secondes
  DateTime _lastAnnouncementTime = DateTime.now();
  List<String> _lastAnnouncedObjects = [];

  // Getters pour l'état
  bool get isInitialized => _isInitialized;
  bool get isDetecting => _isDetecting;
  bool get isModelLoaded => _isModelLoaded;
  bool get isCameraReady => _cameraController?.value.isInitialized ?? false;
  bool get audioFeedbackEnabled => _audioFeedbackEnabled;
  bool get confidenceAnnouncementEnabled => _confidenceAnnouncementEnabled;
  bool get continuousAnnouncementEnabled => _continuousAnnouncementEnabled;

  /// Initialise le service de reconnaissance d'objets
  Future<bool> initialize() async {
    if (_isInitialized) {
      _cleanup();
    }

    try {
      debugPrint('Initialisation du service de reconnaissance d\'objets...');

      // Initialiser la caméra
      await _initializeCamera();

      // Charger le modèle TensorFlow Lite
      await _loadTensorFlowModel();

      // Initialiser le TTS
      await _ttsService.initialize();

      _isInitialized = true;
      debugPrint('Service de reconnaissance initialisé avec succès');
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint(
        'Erreur lors de l\'initialisation du service de reconnaissance: $e',
      );
      _isInitialized = false;
      notifyListeners();
      return false;
    }
  }

  /// Initialise la caméra
  Future<void> _initializeCamera() async {
    try {
      final cameras = await availableCameras();
      if (cameras.isEmpty) {
        throw Exception('Aucune caméra disponible');
      }

      // Utiliser la caméra arrière par défaut
      final camera = cameras.firstWhere(
        (camera) => camera.lensDirection == CameraLensDirection.back,
        orElse: () => cameras.first,
      );

      _cameraController = CameraController(
        camera,
        ResolutionPreset.medium,
        enableAudio: false,
        imageFormatGroup: ImageFormatGroup.yuv420,
      );

      await _cameraController!.initialize();
      notifyListeners();
    } catch (e) {
      debugPrint('Erreur lors de l\'initialisation de la caméra: $e');
      rethrow;
    }
  }

  /// Charge le modèle TensorFlow Lite
  Future<void> _loadTensorFlowModel() async {
    try {
      debugPrint('Chargement du modèle Google ML Kit...');

      final success = await _mlkitDetector.initialize();

      if (success) {
        _isModelLoaded = true;
        debugPrint('Modèle Google ML Kit chargé avec succès');
      } else {
        throw Exception('Échec de l\'initialisation du modèle Google ML Kit');
      }

      notifyListeners();
    } catch (e) {
      debugPrint(
        'Erreur lors de l\'initialisation du modèle Google ML Kit: $e',
      );
      _isModelLoaded = false;
      notifyListeners();
      rethrow;
    }
  }

  /// Démarre la détection en temps réel avec retour audio
  Future<void> startDetection() async {
    if (!_isInitialized || _isDetecting || !isCameraReady) return;

    _isDetecting = true;
    notifyListeners();

    try {
      await _cameraController!.startImageStream(_processImage);

      // Annoncer le démarrage de la détection
      if (_audioFeedbackEnabled) {
        await _ttsService.speak(
          'Détection d\'objets activée. Je vais vous décrire ce que je vois.',
        );
      }
    } catch (e) {
      debugPrint('Erreur lors du démarrage de la détection: $e');
      _isDetecting = false;
      notifyListeners();
    }
  }

  /// Arrête la détection
  Future<void> stopDetection() async {
    if (!_isDetecting) return;

    try {
      await _cameraController?.stopImageStream();
      _isDetecting = false;
      _detectedObjects.clear();
      _lastAnnouncedObjects.clear();
      notifyListeners();

      if (_audioFeedbackEnabled) {
        await _ttsService.speak('Détection d\'objets arrêtée');
      }
    } catch (e) {
      debugPrint('Erreur lors de l\'arrêt de la détection: $e');
    }
  }

  /// Traite une image de la caméra avec retour audio
  void _processImage(CameraImage cameraImage) async {
    if (!_isModelLoaded || !_mlkitDetector.isInitialized) return;

    try {
      final detections = await _mlkitDetector.detectFromCameraImage(
        cameraImage,
      );
      _detectedObjects = detections;
      notifyListeners();

      // Gérer les annonces audio
      await _handleAudioFeedback(detections);
    } catch (e) {
      debugPrint('Erreur lors du traitement de l\'image: $e');
      _detectedObjects = [];
      notifyListeners();
    }
  }

  /// Gère le retour audio pour les utilisateurs malvoyants
  Future<void> _handleAudioFeedback(List<DetectedObject> detections) async {
    if (!_audioFeedbackEnabled || detections.isEmpty) return;

    final now = DateTime.now();

    // Vérifier l'intervalle d'annonce
    if (now.difference(_lastAnnouncementTime).inSeconds <
        _announcementInterval.toInt()) {
      return;
    }

    // Filtrer les objets avec une confiance suffisante
    final significantObjects =
        detections.where((obj) => obj.confidence >= 0.5).toList();

    if (significantObjects.isEmpty) return;

    // Obtenir les noms des objets actuels (traduits en français)
    final currentObjectNames =
        significantObjects
            .map((obj) => translateToFrench(obj.className))
            .toSet()
            .toList();

    // Vérifier s'il y a de nouveaux objets à annoncer
    final newObjects =
        currentObjectNames
            .where((name) => !_lastAnnouncedObjects.contains(name))
            .toList();

    if (newObjects.isNotEmpty || _continuousAnnouncementEnabled) {
      _lastAnnouncementTime = now;

      // Construire le message audio
      final message = _buildAudioMessage(significantObjects);

      try {
        await _ttsService.speak(message);
        _lastAnnouncedObjects = currentObjectNames;
      } catch (e) {
        debugPrint('Erreur lors de l\'annonce audio: $e');
      }
    }
  }

  /// Construit le message audio avec les détails de confiance
  String _buildAudioMessage(List<DetectedObject> objects) {
    if (objects.isEmpty) return '';

    // Grouper les objets par classe
    final objectGroups = <String, List<DetectedObject>>{};
    for (final obj in objects) {
      final classNameFr = translateToFrench(obj.className);
      objectGroups.putIfAbsent(classNameFr, () => []).add(obj);
    }

    final messages = <String>[];

    for (final entry in objectGroups.entries) {
      final className = entry.key;
      final objectsOfClass = entry.value;

      if (objectsOfClass.length == 1) {
        final obj = objectsOfClass.first;
        final confidence = (obj.confidence * 100).round();

        if (_confidenceAnnouncementEnabled) {
          messages.add('$className avec $confidence% de confiance');
        } else {
          messages.add(className);
        }
      } else {
        final avgConfidence =
            (objectsOfClass
                        .map((obj) => obj.confidence)
                        .reduce((a, b) => a + b) /
                    objectsOfClass.length *
                    100)
                .round();

        if (_confidenceAnnouncementEnabled) {
          messages.add(
            '${objectsOfClass.length} $className avec $avgConfidence% de confiance moyenne',
          );
        } else {
          messages.add('${objectsOfClass.length} $className');
        }
      }
    }

    return 'Je vois: ${messages.join(', ')}';
  }

  /// Active/désactive le retour audio
  void toggleAudioFeedback() {
    _audioFeedbackEnabled = !_audioFeedbackEnabled;
    notifyListeners();

    final message =
        _audioFeedbackEnabled
            ? 'Retour audio activé'
            : 'Retour audio désactivé';
    _ttsService.speak(message);
  }

  /// Active/désactive l'annonce du taux de confiance
  void toggleConfidenceAnnouncement() {
    _confidenceAnnouncementEnabled = !_confidenceAnnouncementEnabled;
    notifyListeners();

    final message =
        _confidenceAnnouncementEnabled
            ? 'Annonce de confiance activée'
            : 'Annonce de confiance désactivée';
    _ttsService.speak(message);
  }

  /// Active/désactive les annonces continues
  void toggleContinuousAnnouncement() {
    _continuousAnnouncementEnabled = !_continuousAnnouncementEnabled;
    notifyListeners();

    final message =
        _continuousAnnouncementEnabled
            ? 'Annonces continues activées'
            : 'Annonces continues désactivées';
    _ttsService.speak(message);
  }

  /// Configure l'intervalle d'annonce
  void setAnnouncementInterval(double seconds) {
    _announcementInterval = seconds;
    notifyListeners();
  }

  /// Traduit le nom de classe en français
  String translateToFrench(String className) {
    final translations = {
      'person': 'personne',
      'bicycle': 'vélo',
      'car': 'voiture',
      'motorcycle': 'moto',
      'airplane': 'avion',
      'bus': 'bus',
      'train': 'train',
      'truck': 'camion',
      'boat': 'bateau',
      'traffic light': 'feu de circulation',
      'fire hydrant': 'bouche d\'incendie',
      'stop sign': 'panneau stop',
      'parking meter': 'parcmètre',
      'bench': 'banc',
      'bird': 'oiseau',
      'cat': 'chat',
      'dog': 'chien',
      'horse': 'cheval',
      'sheep': 'mouton',
      'cow': 'vache',
      'elephant': 'éléphant',
      'bear': 'ours',
      'zebra': 'zèbre',
      'giraffe': 'girafe',
      'backpack': 'sac à dos',
      'umbrella': 'parapluie',
      'handbag': 'sac à main',
      'tie': 'cravate',
      'suitcase': 'valise',
      'frisbee': 'frisbee',
      'skis': 'skis',
      'snowboard': 'planche à neige',
      'sports ball': 'ballon de sport',
      'kite': 'cerf-volant',
      'baseball bat': 'batte de baseball',
      'baseball glove': 'gant de baseball',
      'skateboard': 'skateboard',
      'surfboard': 'planche de surf',
      'tennis racket': 'raquette de tennis',
      'bottle': 'bouteille',
      'wine glass': 'verre à vin',
      'cup': 'tasse',
      'fork': 'fourchette',
      'knife': 'couteau',
      'spoon': 'cuillère',
      'bowl': 'bol',
      'banana': 'banane',
      'apple': 'pomme',
      'sandwich': 'sandwich',
      'orange': 'orange',
      'broccoli': 'brocoli',
      'carrot': 'carotte',
      'hot dog': 'hot dog',
      'pizza': 'pizza',
      'donut': 'donut',
      'cake': 'gâteau',
      'chair': 'chaise',
      'couch': 'canapé',
      'potted plant': 'plante en pot',
      'bed': 'lit',
      'dining table': 'table à manger',
      'toilet': 'toilettes',
      'tv': 'télévision',
      'laptop': 'ordinateur portable',
      'mouse': 'souris',
      'remote': 'télécommande',
      'keyboard': 'clavier',
      'cell phone': 'téléphone portable',
      'microwave': 'micro-ondes',
      'oven': 'four',
      'toaster': 'grille-pain',
      'sink': 'évier',
      'refrigerator': 'réfrigérateur',
      'book': 'livre',
      'clock': 'horloge',
      'vase': 'vase',
      'scissors': 'ciseaux',
      'teddy bear': 'ours en peluche',
      'hair drier': 'sèche-cheveux',
      'toothbrush': 'brosse à dents',
    };

    return translations[className.toLowerCase()] ?? className;
  }

  /// Prend une photo et effectue la détection avec retour audio
  Future<List<DetectedObject>> detectFromPhoto() async {
    if (!isCameraReady || !_isModelLoaded || !_mlkitDetector.isInitialized) {
      return [];
    }

    try {
      // Pour la démo, retourner des détections de test
      final detections = [
        DetectedObject(
          className: 'person',
          confidence: 0.85,
          x: 0.2,
          y: 0.3,
          width: 0.3,
          height: 0.5,
        ),
        DetectedObject(
          className: 'bottle',
          confidence: 0.72,
          x: 0.6,
          y: 0.4,
          width: 0.15,
          height: 0.25,
        ),
      ];

      // Annoncer les résultats
      if (_audioFeedbackEnabled && detections.isNotEmpty) {
        final message = _buildAudioMessage(detections);
        await _ttsService.speak(message);
      }

      return detections;
    } catch (e) {
      debugPrint('Erreur lors de la détection sur photo: $e');
      return [];
    }
  }

  /// Configure le seuil de confiance
  void setConfidenceThreshold(double threshold) {
    _mlkitDetector.setConfidenceThreshold(threshold);
  }

  /// Nettoie les ressources
  void _cleanup() {
    _cameraController?.dispose();
    _cameraController = null;
    _mlkitDetector.dispose();
    _isInitialized = false;
    _isDetecting = false;
    _isModelLoaded = false;
    _detectedObjects.clear();
    _lastAnnouncedObjects.clear();
  }

  @override
  void dispose() {
    _cleanup();
    super.dispose();
  }
}

/// Classe représentant un objet détecté
class DetectedObject {
  final String className;
  final double confidence;
  final double x;
  final double y;
  final double width;
  final double height;

  DetectedObject({
    required this.className,
    required this.confidence,
    required this.x,
    required this.y,
    required this.width,
    required this.height,
  });

  factory DetectedObject.fromMap(Map<String, dynamic> map) {
    return DetectedObject(
      className: map['class'] ?? 'Inconnu',
      confidence: (map['confidence'] ?? 0.0).toDouble(),
      x: (map['x'] ?? 0.0).toDouble(),
      y: (map['y'] ?? 0.0).toDouble(),
      width: (map['width'] ?? 0.0).toDouble(),
      height: (map['height'] ?? 0.0).toDouble(),
    );
  }

  /// Retourne le pourcentage de confiance formaté
  String get confidencePercentage =>
      '${(confidence * 100).toStringAsFixed(1)}%';

  /// Retourne le nom de classe traduit en français
  String get classNameFr {
    const translations = {
      'person': 'Personne',
      'car': 'Voiture',
      'dog': 'Chien',
      'cat': 'Chat',
      'chair': 'Chaise',
      'table': 'Table',
      'phone': 'Téléphone',
      'laptop': 'Ordinateur portable',
      'book': 'Livre',
      'bottle': 'Bouteille',
      'cup': 'Tasse',
      'knife': 'Couteau',
      'spoon': 'Cuillère',
      'fork': 'Fourchette',
      'bowl': 'Bol',
      'banana': 'Banane',
      'apple': 'Pomme',
      'orange': 'Orange',
      'clock': 'Horloge',
      'tv': 'Télévision',
      'keyboard': 'Clavier',
      'mouse': 'Souris',
    };

    return translations[className.toLowerCase()] ?? className;
  }
}
