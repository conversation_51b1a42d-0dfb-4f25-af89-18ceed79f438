import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:camera/camera.dart';
import 'package:tflite_flutter/tflite_flutter.dart';
import 'object_recognition_service.dart' as custom;

/// Détecteur TensorFlow Lite utilisant les modèles fournis
/// Implémentation réelle avec TensorFlow Lite
class RealTFLiteDetector {
  static const String _ssdModelPath = 'assets/models/ssd_mobilenet.tflite';
  static const String _labelsPath = 'assets/models/ssd_mobilenet.txt';

  // Interpréteur TensorFlow Lite
  Interpreter? _interpreter;

  // Labels des classes d'objets
  final List<String> _labels = [];

  // Configuration
  bool _isInitialized = false;
  bool get isInitialized => _isInitialized;

  // Paramètres de détection
  double _confidenceThreshold = 0.5;
  final double _nmsThreshold = 0.4;
  static const int _inputSize = 300; // Taille d'entrée pour SSD MobileNet
  static const int _numResults = 10; // Nombre max de détections

  // Cache pour optimiser les détections
  DateTime? _lastDetectionTime;
  final List<custom.DetectedObject> _lastDetections = [];
  static const int _detectionIntervalMs =
      300; // ~3 FPS pour de meilleures performances

  /// Initialise le détecteur avec les vrais modèles TensorFlow Lite
  Future<bool> initialize() async {
    try {
      debugPrint('Initialisation du détecteur TensorFlow Lite...');

      // Charger les labels
      await _loadLabels();

      // Charger le modèle SSD MobileNet
      await _loadModel();

      _isInitialized = true;
      debugPrint('Détecteur TensorFlow Lite initialisé avec succès');
      return true;
    } catch (e) {
      debugPrint('Erreur lors de l\'initialisation du détecteur: $e');
      return false;
    }
  }

  /// Charge les labels des classes d'objets
  Future<void> _loadLabels() async {
    try {
      final labelsData = await rootBundle.loadString(_labelsPath);
      _labels.clear();
      _labels.addAll(
        labelsData.split('\n').where((label) => label.isNotEmpty).toList(),
      );
      debugPrint('${_labels.length} labels chargés');
    } catch (e) {
      debugPrint('Erreur lors du chargement des labels: $e');
      // Labels par défaut si le fichier n'est pas trouvé
      _labels.clear();
      _labels.addAll([
        'person',
        'bicycle',
        'car',
        'motorcycle',
        'airplane',
        'bus',
        'train',
        'truck',
        'boat',
        'traffic light',
        'fire hydrant',
        'stop sign',
        'parking meter',
        'bench',
        'bird',
        'cat',
        'dog',
        'horse',
        'sheep',
        'cow',
        'elephant',
        'bear',
        'zebra',
        'giraffe',
        'backpack',
        'umbrella',
        'handbag',
        'tie',
        'suitcase',
        'frisbee',
        'skis',
        'snowboard',
        'sports ball',
        'kite',
        'baseball bat',
        'baseball glove',
        'skateboard',
        'surfboard',
        'tennis racket',
        'bottle',
        'wine glass',
        'cup',
        'fork',
        'knife',
        'spoon',
        'bowl',
        'banana',
        'apple',
        'sandwich',
        'orange',
        'broccoli',
        'carrot',
        'hot dog',
        'pizza',
        'donut',
        'cake',
        'chair',
        'couch',
        'potted plant',
        'bed',
        'dining table',
        'toilet',
        'tv',
        'laptop',
        'mouse',
        'remote',
        'keyboard',
        'cell phone',
        'microwave',
        'oven',
        'toaster',
        'sink',
        'refrigerator',
        'book',
        'clock',
        'vase',
        'scissors',
        'teddy bear',
        'hair drier',
        'toothbrush',
      ]);
    }
  }

  /// Charge le modèle TensorFlow Lite
  Future<void> _loadModel() async {
    try {
      debugPrint('Chargement du modèle SSD MobileNet...');

      // Charger le modèle SSD MobileNet depuis les assets
      _interpreter = await Interpreter.fromAsset(_ssdModelPath);

      debugPrint('Modèle TensorFlow Lite chargé avec succès');
      debugPrint('Tenseurs d\'entrée: ${_interpreter!.getInputTensors()}');
      debugPrint('Tenseurs de sortie: ${_interpreter!.getOutputTensors()}');
    } catch (e) {
      debugPrint('Erreur lors du chargement du modèle: $e');
      rethrow;
    }
  }

  /// Détecte les objets dans une image de caméra
  Future<List<custom.DetectedObject>> detectFromCameraImage(
    CameraImage cameraImage,
  ) async {
    if (!_isInitialized || _interpreter == null) return [];

    try {
      // Optimisation : limiter la fréquence de détection
      final now = DateTime.now();
      if (_lastDetectionTime != null &&
          now.difference(_lastDetectionTime!).inMilliseconds <
              _detectionIntervalMs) {
        return _lastDetections;
      }
      _lastDetectionTime = now;

      // Convertir l'image de la caméra en format utilisable
      final inputImage = _preprocessCameraImage(cameraImage);

      // Préparer les tenseurs de sortie
      final outputs = _prepareOutputTensors();

      // Exécuter l'inférence TensorFlow Lite
      _interpreter!.runForMultipleInputs([inputImage], outputs);

      // Parser les résultats
      final detections = _parseDetectionResults(outputs);

      // Filtrer par seuil de confiance
      final filteredDetections =
          detections
              .where((obj) => obj.confidence >= _confidenceThreshold)
              .toList();

      _lastDetections.clear();
      _lastDetections.addAll(filteredDetections);

      return filteredDetections;
    } catch (e) {
      debugPrint('Erreur lors de la détection sur image caméra: $e');
      return [];
    }
  }

  /// Génère des détections de démonstration
  List<custom.DetectedObject> _generateDemoDetections() {
    final random = math.Random();
    final detections = <custom.DetectedObject>[];

    // Simuler 1-3 objets détectés
    final numObjects = 1 + random.nextInt(3);

    for (int i = 0; i < numObjects; i++) {
      final objectIndex = random.nextInt(_labels.length);
      if (objectIndex < _labels.length) {
        detections.add(
          custom.DetectedObject(
            className: _labels[objectIndex],
            confidence: 0.6 + random.nextDouble() * 0.3, // 0.6-0.9
            x: random.nextDouble() * 0.6, // 0-0.6
            y: random.nextDouble() * 0.6, // 0-0.6
            width: 0.2 + random.nextDouble() * 0.3, // 0.2-0.5
            height: 0.2 + random.nextDouble() * 0.3, // 0.2-0.5
          ),
        );
      }
    }

    return detections;
  }

  /// Préprocesse l'image de la caméra pour l'inférence TensorFlow Lite
  List<List<List<List<double>>>> _preprocessCameraImage(
    CameraImage cameraImage,
  ) {
    // Convertir YUV420 en RGB et redimensionner à 300x300
    final input = List.generate(
      1,
      (index) => List.generate(
        _inputSize,
        (y) => List.generate(_inputSize, (x) => List.generate(3, (c) => 0.0)),
      ),
    );

    // Utiliser le plan Y (luminance) pour simplifier
    final yPlane = cameraImage.planes[0];
    final yBytes = yPlane.bytes;

    final scaleX = cameraImage.width / _inputSize;
    final scaleY = cameraImage.height / _inputSize;

    for (int y = 0; y < _inputSize; y++) {
      for (int x = 0; x < _inputSize; x++) {
        final srcX = (x * scaleX).round().clamp(0, cameraImage.width - 1);
        final srcY = (y * scaleY).round().clamp(0, cameraImage.height - 1);

        final pixelIndex = srcY * yPlane.bytesPerRow + srcX;
        if (pixelIndex < yBytes.length) {
          final pixelValue = yBytes[pixelIndex] / 255.0;
          // Convertir en RGB (grayscale)
          input[0][y][x][0] = pixelValue; // R
          input[0][y][x][1] = pixelValue; // G
          input[0][y][x][2] = pixelValue; // B
        }
      }
    }

    return input;
  }

  /// Prépare les tenseurs de sortie pour SSD MobileNet
  Map<int, Object> _prepareOutputTensors() {
    return {
      0: List.generate(
        1,
        (index) => List.filled(_numResults * 4, 0.0),
      ), // locations [y1, x1, y2, x2]
      1: List.generate(1, (index) => List.filled(_numResults, 0.0)), // classes
      2: List.generate(1, (index) => List.filled(_numResults, 0.0)), // scores
      3: List.generate(1, (index) => 0.0), // number of detections
    };
  }

  /// Parse les résultats de détection du modèle SSD MobileNet
  List<custom.DetectedObject> _parseDetectionResults(Map<int, Object> outputs) {
    final detections = <custom.DetectedObject>[];

    try {
      final locations = outputs[0] as List<List<double>>;
      final classes = outputs[1] as List<List<double>>;
      final scores = outputs[2] as List<List<double>>;
      final numDetections = (outputs[3] as List<double>)[0].round();

      for (int i = 0; i < math.min(numDetections, _numResults); i++) {
        final score = scores[0][i];
        if (score < _confidenceThreshold) continue;

        final classId = classes[0][i].round();
        if (classId < 0 || classId >= _labels.length) continue;

        // Les coordonnées sont normalisées [0, 1]
        final y1 = locations[0][i * 4];
        final x1 = locations[0][i * 4 + 1];
        final y2 = locations[0][i * 4 + 2];
        final x2 = locations[0][i * 4 + 3];

        detections.add(
          custom.DetectedObject(
            className: _labels[classId],
            confidence: score,
            x: x1,
            y: y1,
            width: x2 - x1,
            height: y2 - y1,
          ),
        );
      }

      return _applyNMS(detections);
    } catch (e) {
      debugPrint('Erreur lors du parsing des résultats: $e');
      return [];
    }
  }

  /// Applique Non-Maximum Suppression
  List<custom.DetectedObject> _applyNMS(
    List<custom.DetectedObject> detections,
  ) {
    if (detections.isEmpty) return [];

    // Trier par confiance décroissante
    detections.sort((a, b) => b.confidence.compareTo(a.confidence));

    final result = <custom.DetectedObject>[];
    final suppressed = List<bool>.filled(detections.length, false);

    for (int i = 0; i < detections.length; i++) {
      if (suppressed[i]) continue;

      result.add(detections[i]);

      for (int j = i + 1; j < detections.length; j++) {
        if (suppressed[j]) continue;

        // Vérifier si les boîtes se chevauchent
        if (_calculateIoU(detections[i], detections[j]) > _nmsThreshold) {
          suppressed[j] = true;
        }
      }
    }

    return result;
  }

  /// Calcule l'IoU (Intersection over Union)
  double _calculateIoU(custom.DetectedObject box1, custom.DetectedObject box2) {
    final x1 = math.max(box1.x, box2.x);
    final y1 = math.max(box1.y, box2.y);
    final x2 = math.min(box1.x + box1.width, box2.x + box2.width);
    final y2 = math.min(box1.y + box1.height, box2.y + box2.height);

    if (x2 <= x1 || y2 <= y1) return 0.0;

    final intersection = (x2 - x1) * (y2 - y1);
    final area1 = box1.width * box1.height;
    final area2 = box2.width * box2.height;
    final union = area1 + area2 - intersection;

    return intersection / union;
  }

  /// Configure le seuil de confiance
  void setConfidenceThreshold(double threshold) {
    _confidenceThreshold = threshold.clamp(0.0, 1.0);
  }

  /// Libère les ressources
  void dispose() {
    _interpreter?.close();
    _interpreter = null;
    _isInitialized = false;
  }
}
