# Reconnaissance d'Objets pour Utilisateurs Malvoyants

## Vue d'ensemble

Cette fonctionnalité de reconnaissance d'objets en temps réel est spécialement conçue pour les utilisateurs malvoyants, offrant un retour audio détaillé sur l'environnement visuel capturé par la caméra.

## Fonctionnalités Principales

### 🎯 Détection en Temps Réel
- **Reconnaissance automatique** : Détecte automatiquement les objets dans le champ de vision de la caméra
- **Taux de fiabilité** : Annonce le pourcentage de confiance pour chaque objet détecté
- **Traduction française** : Tous les noms d'objets sont traduits en français pour une meilleure compréhension

### 🔊 Retour Audio Adapté
- **Annonces intelligentes** : Décrit ce qui est vu avec des messages naturels
- **Contrôles audio configurables** :
  - Retour audio activé/désactivé
  - Annonce du taux de confiance
  - Annonces continues ou ponctuelles
- **Messages contextuels** : "Je vois une personne avec 85% de confiance"

### 🎮 Interface Accessible
- **Contrôles tactiles** : Boutons larges et bien espacés
- **Retour haptique** : Vibrations lors des interactions
- **Navigation vocale** : Tous les éléments sont annoncés vocalement
- **Thème adaptatif** : Support des thèmes clair et sombre

## Utilisation

### Démarrage Rapide
1. **Ouvrir l'application** et naviguer vers "Reconnaissance d'Objets"
2. **Autoriser la caméra** quand demandé
3. **Appuyer sur "Démarrer"** pour lancer la détection
4. **Écouter les annonces** qui décrivent l'environnement

### Contrôles Principaux

#### Boutons Flottants
- **🟢 Démarrer/Arrêter** : Active ou désactive la détection
- **📷 Photo** : Prend une photo et analyse les objets
- **🔊 Annoncer** : Répète la description des objets actuels

#### Contrôles Audio
- **Retour Audio** : Active/désactive toutes les annonces
- **Annonce Confiance** : Inclut le pourcentage de fiabilité
- **Annonces Continues** : Répète les descriptions régulièrement

### Messages Audio Exemples

```
"Détection d'objets activée. Je vais vous décrire ce que je vois."
"Je vois: personne avec 85% de confiance, chaise avec 72% de confiance"
"Prise de photo en cours..."
"J'ai détecté: voiture avec 78% de confiance"
```

## Modèles de Reconnaissance

### Modèles TensorFlow Lite Disponibles
- **YOLO v2 Tiny** : Détection rapide et précise
- **SSD MobileNet** : Reconnaissance d'objets multiples
- **COCO Dataset** : 80 classes d'objets courants

### Classes d'Objets Détectées
- **Personnes** : personne, enfant, adulte
- **Véhicules** : voiture, bus, moto, vélo
- **Mobilier** : chaise, table, lit, canapé
- **Électronique** : téléphone, ordinateur, télévision
- **Objets quotidiens** : bouteille, tasse, livre, clés
- **Et bien plus...**

## Configuration Avancée

### Seuils de Confiance
- **Seuil par défaut** : 50% (objets détectés avec 50%+ de confiance)
- **Ajustable** : Modifiable selon les besoins
- **Filtrage intelligent** : Évite les fausses détections

### Performance
- **Optimisation** : Limitation à 2 FPS pour économiser la batterie
- **Cache intelligent** : Évite les annonces répétitives
- **Gestion mémoire** : Libération automatique des ressources

## Accessibilité

### Standards Respectés
- **WCAG 2.1** : Conformité aux standards d'accessibilité
- **Navigation clavier** : Support complet du clavier
- **Lecteurs d'écran** : Compatible avec TalkBack et VoiceOver

### Fonctionnalités d'Accessibilité
- **Contraste élevé** : Textes et boutons bien visibles
- **Tailles de police** : Adaptables aux préférences système
- **Animations réduites** : Respect des préférences d'accessibilité

## Dépannage

### Problèmes Courants

#### Caméra non accessible
```
Solution : Vérifier les permissions dans les paramètres de l'appareil
```

#### Détection lente
```
Solution : Réduire la résolution de la caméra ou désactiver les annonces continues
```

#### Annonces trop fréquentes
```
Solution : Augmenter l'intervalle d'annonce ou désactiver les annonces continues
```

#### Batterie qui se vide rapidement
```
Solution : Utiliser la détection ponctuelle plutôt que continue
```

### Optimisations Recommandées

1. **Éclairage** : Assurer un bon éclairage pour de meilleures détections
2. **Distance** : Maintenir une distance appropriée (1-3 mètres)
3. **Stabilité** : Tenir l'appareil stable pour des résultats optimaux
4. **Nettoyage** : Garder l'objectif de la caméra propre

## Support Technique

### Logs de Débogage
Les logs détaillés sont disponibles pour diagnostiquer les problèmes :
- Initialisation des modèles
- Performance de détection
- Erreurs de reconnaissance

### Statistiques de Performance
- Nombre total de détections
- Taux de succès
- Durée de session
- Modèle utilisé

## Développement

### Architecture
- **Service de reconnaissance** : `ObjectRecognitionService`
- **Détecteur YOLO** : `YoloDetector`
- **Interface utilisateur** : `ObjectRecognitionPage`
- **Système TTS** : Intégré pour les annonces

### Extensibilité
- **Nouveaux modèles** : Facilement ajoutables
- **Nouvelles classes** : Support des datasets personnalisés
- **Langues** : Traductions extensibles

## Conclusion

Cette fonctionnalité transforme la reconnaissance d'objets en un outil d'assistance précieux pour les utilisateurs malvoyants, offrant une perception de l'environnement visuel à travers des descriptions audio détaillées et naturelles.

---

*Développé avec ❤️ pour l'accessibilité numérique* 