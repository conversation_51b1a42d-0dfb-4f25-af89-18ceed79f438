import 'package:flutter_test/flutter_test.dart';
import 'package:voice_assistant/services/real_tflite_detector.dart';

void main() {
  group('RealTFLiteDetector Tests', () {
    late RealTFLiteDetector detector;

    setUp(() {
      detector = RealTFLiteDetector();
    });

    test('should initialize successfully', () async {
      final result = await detector.initialize();
      expect(result, isTrue);
      expect(detector.isInitialized, isTrue);
    });

    test('should set confidence threshold correctly', () {
      detector.setConfidenceThreshold(0.7);
      // Le seuil est privé, mais on peut tester indirectement
      expect(detector.isInitialized, isFalse); // Avant initialisation
    });

    test('should dispose resources properly', () {
      detector.dispose();
      expect(detector.isInitialized, isFalse);
    });
  });
}
