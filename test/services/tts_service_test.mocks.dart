// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in voice_assistant/test/services/tts_service_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;
import 'dart:ui' as _i4;

import 'package:flutter/services.dart' as _i5;
import 'package:flutter_tts/flutter_tts.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i7;
import 'package:voice_assistant/services/voice_detection_service.dart' as _i6;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeSpeechRateValidRange_0 extends _i1.SmartFake
    implements _i2.SpeechRateValidRange {
  _FakeSpeechRateValidRange_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [FlutterTts].
///
/// See the documentation for Mockito's code generation for more information.
class MockFlutterTts extends _i1.Mock implements _i2.FlutterTts {
  @override
  _i3.Future<int?> get getMaxSpeechInputLength =>
      (super.noSuchMethod(
            Invocation.getter(#getMaxSpeechInputLength),
            returnValue: _i3.Future<int?>.value(),
            returnValueForMissingStub: _i3.Future<int?>.value(),
          )
          as _i3.Future<int?>);

  @override
  _i3.Future<dynamic> get getLanguages =>
      (super.noSuchMethod(
            Invocation.getter(#getLanguages),
            returnValue: _i3.Future<dynamic>.value(),
            returnValueForMissingStub: _i3.Future<dynamic>.value(),
          )
          as _i3.Future<dynamic>);

  @override
  _i3.Future<dynamic> get getEngines =>
      (super.noSuchMethod(
            Invocation.getter(#getEngines),
            returnValue: _i3.Future<dynamic>.value(),
            returnValueForMissingStub: _i3.Future<dynamic>.value(),
          )
          as _i3.Future<dynamic>);

  @override
  _i3.Future<dynamic> get getDefaultEngine =>
      (super.noSuchMethod(
            Invocation.getter(#getDefaultEngine),
            returnValue: _i3.Future<dynamic>.value(),
            returnValueForMissingStub: _i3.Future<dynamic>.value(),
          )
          as _i3.Future<dynamic>);

  @override
  _i3.Future<dynamic> get getDefaultVoice =>
      (super.noSuchMethod(
            Invocation.getter(#getDefaultVoice),
            returnValue: _i3.Future<dynamic>.value(),
            returnValueForMissingStub: _i3.Future<dynamic>.value(),
          )
          as _i3.Future<dynamic>);

  @override
  _i3.Future<dynamic> get getVoices =>
      (super.noSuchMethod(
            Invocation.getter(#getVoices),
            returnValue: _i3.Future<dynamic>.value(),
            returnValueForMissingStub: _i3.Future<dynamic>.value(),
          )
          as _i3.Future<dynamic>);

  @override
  _i3.Future<_i2.SpeechRateValidRange> get getSpeechRateValidRange =>
      (super.noSuchMethod(
            Invocation.getter(#getSpeechRateValidRange),
            returnValue: _i3.Future<_i2.SpeechRateValidRange>.value(
              _FakeSpeechRateValidRange_0(
                this,
                Invocation.getter(#getSpeechRateValidRange),
              ),
            ),
            returnValueForMissingStub:
                _i3.Future<_i2.SpeechRateValidRange>.value(
                  _FakeSpeechRateValidRange_0(
                    this,
                    Invocation.getter(#getSpeechRateValidRange),
                  ),
                ),
          )
          as _i3.Future<_i2.SpeechRateValidRange>);

  @override
  set startHandler(_i4.VoidCallback? _startHandler) => super.noSuchMethod(
    Invocation.setter(#startHandler, _startHandler),
    returnValueForMissingStub: null,
  );

  @override
  set initHandler(_i4.VoidCallback? _initHandler) => super.noSuchMethod(
    Invocation.setter(#initHandler, _initHandler),
    returnValueForMissingStub: null,
  );

  @override
  set completionHandler(_i4.VoidCallback? _completionHandler) =>
      super.noSuchMethod(
        Invocation.setter(#completionHandler, _completionHandler),
        returnValueForMissingStub: null,
      );

  @override
  set pauseHandler(_i4.VoidCallback? _pauseHandler) => super.noSuchMethod(
    Invocation.setter(#pauseHandler, _pauseHandler),
    returnValueForMissingStub: null,
  );

  @override
  set continueHandler(_i4.VoidCallback? _continueHandler) => super.noSuchMethod(
    Invocation.setter(#continueHandler, _continueHandler),
    returnValueForMissingStub: null,
  );

  @override
  set cancelHandler(_i4.VoidCallback? _cancelHandler) => super.noSuchMethod(
    Invocation.setter(#cancelHandler, _cancelHandler),
    returnValueForMissingStub: null,
  );

  @override
  set progressHandler(_i2.ProgressHandler? _progressHandler) =>
      super.noSuchMethod(
        Invocation.setter(#progressHandler, _progressHandler),
        returnValueForMissingStub: null,
      );

  @override
  set errorHandler(_i2.ErrorHandler? _errorHandler) => super.noSuchMethod(
    Invocation.setter(#errorHandler, _errorHandler),
    returnValueForMissingStub: null,
  );

  @override
  _i3.Future<dynamic> awaitSpeakCompletion(bool? awaitCompletion) =>
      (super.noSuchMethod(
            Invocation.method(#awaitSpeakCompletion, [awaitCompletion]),
            returnValue: _i3.Future<dynamic>.value(),
            returnValueForMissingStub: _i3.Future<dynamic>.value(),
          )
          as _i3.Future<dynamic>);

  @override
  _i3.Future<dynamic> awaitSynthCompletion(bool? awaitCompletion) =>
      (super.noSuchMethod(
            Invocation.method(#awaitSynthCompletion, [awaitCompletion]),
            returnValue: _i3.Future<dynamic>.value(),
            returnValueForMissingStub: _i3.Future<dynamic>.value(),
          )
          as _i3.Future<dynamic>);

  @override
  _i3.Future<dynamic> speak(String? text) =>
      (super.noSuchMethod(
            Invocation.method(#speak, [text]),
            returnValue: _i3.Future<dynamic>.value(),
            returnValueForMissingStub: _i3.Future<dynamic>.value(),
          )
          as _i3.Future<dynamic>);

  @override
  _i3.Future<dynamic> pause() =>
      (super.noSuchMethod(
            Invocation.method(#pause, []),
            returnValue: _i3.Future<dynamic>.value(),
            returnValueForMissingStub: _i3.Future<dynamic>.value(),
          )
          as _i3.Future<dynamic>);

  @override
  _i3.Future<dynamic> synthesizeToFile(String? text, String? fileName) =>
      (super.noSuchMethod(
            Invocation.method(#synthesizeToFile, [text, fileName]),
            returnValue: _i3.Future<dynamic>.value(),
            returnValueForMissingStub: _i3.Future<dynamic>.value(),
          )
          as _i3.Future<dynamic>);

  @override
  _i3.Future<dynamic> setLanguage(String? language) =>
      (super.noSuchMethod(
            Invocation.method(#setLanguage, [language]),
            returnValue: _i3.Future<dynamic>.value(),
            returnValueForMissingStub: _i3.Future<dynamic>.value(),
          )
          as _i3.Future<dynamic>);

  @override
  _i3.Future<dynamic> setSpeechRate(double? rate) =>
      (super.noSuchMethod(
            Invocation.method(#setSpeechRate, [rate]),
            returnValue: _i3.Future<dynamic>.value(),
            returnValueForMissingStub: _i3.Future<dynamic>.value(),
          )
          as _i3.Future<dynamic>);

  @override
  _i3.Future<dynamic> setVolume(double? volume) =>
      (super.noSuchMethod(
            Invocation.method(#setVolume, [volume]),
            returnValue: _i3.Future<dynamic>.value(),
            returnValueForMissingStub: _i3.Future<dynamic>.value(),
          )
          as _i3.Future<dynamic>);

  @override
  _i3.Future<dynamic> setSharedInstance(bool? sharedSession) =>
      (super.noSuchMethod(
            Invocation.method(#setSharedInstance, [sharedSession]),
            returnValue: _i3.Future<dynamic>.value(),
            returnValueForMissingStub: _i3.Future<dynamic>.value(),
          )
          as _i3.Future<dynamic>);

  @override
  _i3.Future<dynamic> autoStopSharedSession(bool? autoStop) =>
      (super.noSuchMethod(
            Invocation.method(#autoStopSharedSession, [autoStop]),
            returnValue: _i3.Future<dynamic>.value(),
            returnValueForMissingStub: _i3.Future<dynamic>.value(),
          )
          as _i3.Future<dynamic>);

  @override
  _i3.Future<dynamic> setIosAudioCategory(
    _i2.IosTextToSpeechAudioCategory? category,
    List<_i2.IosTextToSpeechAudioCategoryOptions>? options, [
    _i2.IosTextToSpeechAudioMode? mode =
        _i2.IosTextToSpeechAudioMode.defaultMode,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#setIosAudioCategory, [category, options, mode]),
            returnValue: _i3.Future<dynamic>.value(),
            returnValueForMissingStub: _i3.Future<dynamic>.value(),
          )
          as _i3.Future<dynamic>);

  @override
  _i3.Future<dynamic> setEngine(String? engine) =>
      (super.noSuchMethod(
            Invocation.method(#setEngine, [engine]),
            returnValue: _i3.Future<dynamic>.value(),
            returnValueForMissingStub: _i3.Future<dynamic>.value(),
          )
          as _i3.Future<dynamic>);

  @override
  _i3.Future<dynamic> setPitch(double? pitch) =>
      (super.noSuchMethod(
            Invocation.method(#setPitch, [pitch]),
            returnValue: _i3.Future<dynamic>.value(),
            returnValueForMissingStub: _i3.Future<dynamic>.value(),
          )
          as _i3.Future<dynamic>);

  @override
  _i3.Future<dynamic> setVoice(Map<String, String>? voice) =>
      (super.noSuchMethod(
            Invocation.method(#setVoice, [voice]),
            returnValue: _i3.Future<dynamic>.value(),
            returnValueForMissingStub: _i3.Future<dynamic>.value(),
          )
          as _i3.Future<dynamic>);

  @override
  _i3.Future<dynamic> stop() =>
      (super.noSuchMethod(
            Invocation.method(#stop, []),
            returnValue: _i3.Future<dynamic>.value(),
            returnValueForMissingStub: _i3.Future<dynamic>.value(),
          )
          as _i3.Future<dynamic>);

  @override
  _i3.Future<dynamic> isLanguageAvailable(String? language) =>
      (super.noSuchMethod(
            Invocation.method(#isLanguageAvailable, [language]),
            returnValue: _i3.Future<dynamic>.value(),
            returnValueForMissingStub: _i3.Future<dynamic>.value(),
          )
          as _i3.Future<dynamic>);

  @override
  _i3.Future<dynamic> isLanguageInstalled(String? language) =>
      (super.noSuchMethod(
            Invocation.method(#isLanguageInstalled, [language]),
            returnValue: _i3.Future<dynamic>.value(),
            returnValueForMissingStub: _i3.Future<dynamic>.value(),
          )
          as _i3.Future<dynamic>);

  @override
  _i3.Future<dynamic> areLanguagesInstalled(List<String>? languages) =>
      (super.noSuchMethod(
            Invocation.method(#areLanguagesInstalled, [languages]),
            returnValue: _i3.Future<dynamic>.value(),
            returnValueForMissingStub: _i3.Future<dynamic>.value(),
          )
          as _i3.Future<dynamic>);

  @override
  _i3.Future<dynamic> setSilence(int? timems) =>
      (super.noSuchMethod(
            Invocation.method(#setSilence, [timems]),
            returnValue: _i3.Future<dynamic>.value(),
            returnValueForMissingStub: _i3.Future<dynamic>.value(),
          )
          as _i3.Future<dynamic>);

  @override
  _i3.Future<dynamic> setQueueMode(int? queueMode) =>
      (super.noSuchMethod(
            Invocation.method(#setQueueMode, [queueMode]),
            returnValue: _i3.Future<dynamic>.value(),
            returnValueForMissingStub: _i3.Future<dynamic>.value(),
          )
          as _i3.Future<dynamic>);

  @override
  void setStartHandler(_i4.VoidCallback? callback) => super.noSuchMethod(
    Invocation.method(#setStartHandler, [callback]),
    returnValueForMissingStub: null,
  );

  @override
  void setInitHandler(_i4.VoidCallback? callback) => super.noSuchMethod(
    Invocation.method(#setInitHandler, [callback]),
    returnValueForMissingStub: null,
  );

  @override
  void setCompletionHandler(_i4.VoidCallback? callback) => super.noSuchMethod(
    Invocation.method(#setCompletionHandler, [callback]),
    returnValueForMissingStub: null,
  );

  @override
  void setContinueHandler(_i4.VoidCallback? callback) => super.noSuchMethod(
    Invocation.method(#setContinueHandler, [callback]),
    returnValueForMissingStub: null,
  );

  @override
  void setPauseHandler(_i4.VoidCallback? callback) => super.noSuchMethod(
    Invocation.method(#setPauseHandler, [callback]),
    returnValueForMissingStub: null,
  );

  @override
  void setCancelHandler(_i4.VoidCallback? callback) => super.noSuchMethod(
    Invocation.method(#setCancelHandler, [callback]),
    returnValueForMissingStub: null,
  );

  @override
  void setProgressHandler(_i2.ProgressHandler? callback) => super.noSuchMethod(
    Invocation.method(#setProgressHandler, [callback]),
    returnValueForMissingStub: null,
  );

  @override
  void setErrorHandler(_i2.ErrorHandler? handler) => super.noSuchMethod(
    Invocation.method(#setErrorHandler, [handler]),
    returnValueForMissingStub: null,
  );

  @override
  _i3.Future<dynamic> platformCallHandler(_i5.MethodCall? call) =>
      (super.noSuchMethod(
            Invocation.method(#platformCallHandler, [call]),
            returnValue: _i3.Future<dynamic>.value(),
            returnValueForMissingStub: _i3.Future<dynamic>.value(),
          )
          as _i3.Future<dynamic>);
}

/// A class which mocks [VoiceDetectionService].
///
/// See the documentation for Mockito's code generation for more information.
class MockVoiceDetectionService extends _i1.Mock
    implements _i6.VoiceDetectionService {
  @override
  void Function(String, {bool isReminderReadCommand}) get commandDetected =>
      (super.noSuchMethod(
            Invocation.getter(#commandDetected),
            returnValue: (String command, {bool? isReminderReadCommand}) {},
            returnValueForMissingStub:
                (String command, {bool? isReminderReadCommand}) {},
          )
          as void Function(String, {bool isReminderReadCommand}));

  @override
  String get recognizedText =>
      (super.noSuchMethod(
            Invocation.getter(#recognizedText),
            returnValue: _i7.dummyValue<String>(
              this,
              Invocation.getter(#recognizedText),
            ),
            returnValueForMissingStub: _i7.dummyValue<String>(
              this,
              Invocation.getter(#recognizedText),
            ),
          )
          as String);

  @override
  bool get isSpeaking =>
      (super.noSuchMethod(
            Invocation.getter(#isSpeaking),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  double get soundLevel =>
      (super.noSuchMethod(
            Invocation.getter(#soundLevel),
            returnValue: 0.0,
            returnValueForMissingStub: 0.0,
          )
          as double);

  @override
  _i6.VoiceState get state =>
      (super.noSuchMethod(
            Invocation.getter(#state),
            returnValue: _i6.VoiceState.idle,
            returnValueForMissingStub: _i6.VoiceState.idle,
          )
          as _i6.VoiceState);

  @override
  bool get continuousListening =>
      (super.noSuchMethod(
            Invocation.getter(#continuousListening),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  set commandDetected(
    void Function(String, {bool isReminderReadCommand})? _commandDetected,
  ) => super.noSuchMethod(
    Invocation.setter(#commandDetected, _commandDetected),
    returnValueForMissingStub: null,
  );

  @override
  set continuousListening(bool? value) => super.noSuchMethod(
    Invocation.setter(#continuousListening, value),
    returnValueForMissingStub: null,
  );

  @override
  bool get hasListeners =>
      (super.noSuchMethod(
            Invocation.getter(#hasListeners),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  _i3.Future<bool> initialize() =>
      (super.noSuchMethod(
            Invocation.method(#initialize, []),
            returnValue: _i3.Future<bool>.value(false),
            returnValueForMissingStub: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  void toggleContinuousListening() => super.noSuchMethod(
    Invocation.method(#toggleContinuousListening, []),
    returnValueForMissingStub: null,
  );

  @override
  void startContinuousListening() => super.noSuchMethod(
    Invocation.method(#startContinuousListening, []),
    returnValueForMissingStub: null,
  );

  @override
  void stopListening() => super.noSuchMethod(
    Invocation.method(#stopListening, []),
    returnValueForMissingStub: null,
  );

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  void addListener(_i4.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#addListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void removeListener(_i4.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#removeListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void notifyListeners() => super.noSuchMethod(
    Invocation.method(#notifyListeners, []),
    returnValueForMissingStub: null,
  );
}
