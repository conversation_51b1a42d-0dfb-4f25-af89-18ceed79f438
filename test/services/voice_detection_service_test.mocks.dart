// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in voice_assistant/test/services/voice_detection_service_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i3;
import 'package:speech_to_text/speech_to_text.dart' as _i2;
import 'package:speech_to_text_platform_interface/speech_to_text_platform_interface.dart'
    as _i5;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [SpeechToText].
///
/// See the documentation for Mockito's code generation for more information.
class MockSpeechToText extends _i1.Mock implements _i2.SpeechToText {
  MockSpeechToText() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get hasRecognized =>
      (super.noSuchMethod(Invocation.getter(#hasRecognized), returnValue: false)
          as bool);

  @override
  String get lastRecognizedWords =>
      (super.noSuchMethod(
            Invocation.getter(#lastRecognizedWords),
            returnValue: _i3.dummyValue<String>(
              this,
              Invocation.getter(#lastRecognizedWords),
            ),
          )
          as String);

  @override
  String get lastStatus =>
      (super.noSuchMethod(
            Invocation.getter(#lastStatus),
            returnValue: _i3.dummyValue<String>(
              this,
              Invocation.getter(#lastStatus),
            ),
          )
          as String);

  @override
  double get lastSoundLevel =>
      (super.noSuchMethod(Invocation.getter(#lastSoundLevel), returnValue: 0.0)
          as double);

  @override
  bool get isAvailable =>
      (super.noSuchMethod(Invocation.getter(#isAvailable), returnValue: false)
          as bool);

  @override
  bool get isListening =>
      (super.noSuchMethod(Invocation.getter(#isListening), returnValue: false)
          as bool);

  @override
  bool get isNotListening =>
      (super.noSuchMethod(
            Invocation.getter(#isNotListening),
            returnValue: false,
          )
          as bool);

  @override
  bool get hasError =>
      (super.noSuchMethod(Invocation.getter(#hasError), returnValue: false)
          as bool);

  @override
  _i4.Future<bool> get hasPermission =>
      (super.noSuchMethod(
            Invocation.getter(#hasPermission),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  set errorListener(_i2.SpeechErrorListener? _errorListener) =>
      super.noSuchMethod(
        Invocation.setter(#errorListener, _errorListener),
        returnValueForMissingStub: null,
      );

  @override
  set statusListener(_i2.SpeechStatusListener? _statusListener) =>
      super.noSuchMethod(
        Invocation.setter(#statusListener, _statusListener),
        returnValueForMissingStub: null,
      );

  @override
  set unexpectedPhraseAggregator(
    _i2.SpeechPhraseAggregator? _unexpectedPhraseAggregator,
  ) => super.noSuchMethod(
    Invocation.setter(#unexpectedPhraseAggregator, _unexpectedPhraseAggregator),
    returnValueForMissingStub: null,
  );

  @override
  _i4.Future<bool> initialize({
    _i2.SpeechErrorListener? onError,
    _i2.SpeechStatusListener? onStatus,
    dynamic debugLogging = false,
    Duration? finalTimeout = const Duration(milliseconds: 2000),
    List<_i5.SpeechConfigOption>? options,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#initialize, [], {
              #onError: onError,
              #onStatus: onStatus,
              #debugLogging: debugLogging,
              #finalTimeout: finalTimeout,
              #options: options,
            }),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<void> stop() =>
      (super.noSuchMethod(
            Invocation.method(#stop, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> cancel() =>
      (super.noSuchMethod(
            Invocation.method(#cancel, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<dynamic> listen({
    _i2.SpeechResultListener? onResult,
    Duration? listenFor,
    Duration? pauseFor,
    String? localeId,
    _i2.SpeechSoundLevelChange? onSoundLevelChange,
    dynamic cancelOnError = false,
    dynamic partialResults = true,
    dynamic onDevice = false,
    _i5.ListenMode? listenMode = _i5.ListenMode.confirmation,
    dynamic sampleRate = 0,
    _i5.SpeechListenOptions? listenOptions,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#listen, [], {
              #onResult: onResult,
              #listenFor: listenFor,
              #pauseFor: pauseFor,
              #localeId: localeId,
              #onSoundLevelChange: onSoundLevelChange,
              #cancelOnError: cancelOnError,
              #partialResults: partialResults,
              #onDevice: onDevice,
              #listenMode: listenMode,
              #sampleRate: sampleRate,
              #listenOptions: listenOptions,
            }),
            returnValue: _i4.Future<dynamic>.value(),
          )
          as _i4.Future<dynamic>);

  @override
  void changePauseFor(Duration? pauseFor) => super.noSuchMethod(
    Invocation.method(#changePauseFor, [pauseFor]),
    returnValueForMissingStub: null,
  );

  @override
  _i4.Future<List<_i2.LocaleName>> locales() =>
      (super.noSuchMethod(
            Invocation.method(#locales, []),
            returnValue: _i4.Future<List<_i2.LocaleName>>.value(
              <_i2.LocaleName>[],
            ),
          )
          as _i4.Future<List<_i2.LocaleName>>);

  @override
  _i4.Future<_i2.LocaleName?> systemLocale() =>
      (super.noSuchMethod(
            Invocation.method(#systemLocale, []),
            returnValue: _i4.Future<_i2.LocaleName?>.value(),
          )
          as _i4.Future<_i2.LocaleName?>);
}
